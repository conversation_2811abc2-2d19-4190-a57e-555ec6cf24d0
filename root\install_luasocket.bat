@echo off
chcp 65001 >nul
echo 安装luasocket到本地luarocks环境...

set CURRENT_DIR=%cd%
set LUAROCKS_DIR=%CURRENT_DIR%\..\luarocks-3.12.2-win32
set LUA_DIR=%LUAROCKS_DIR%\win32\lua5.1

echo 当前目录: %CURRENT_DIR%
echo Lua目录: %LUA_DIR%
echo LuaRocks目录: %LUAROCKS_DIR%

if not exist "%LUA_DIR%\bin\lua5.1.exe" (
    echo 错误: 找不到lua5.1.exe
    echo 路径: %LUA_DIR%\bin\lua5.1.exe
    pause
    exit /b 1
)

if not exist "rocks" mkdir rocks
if not exist "rocks\lib" mkdir rocks\lib
if not exist "rocks\share" mkdir rocks\share

echo 检查Lua安装...
"%LUA_DIR%\bin\lua5.1.exe" -v

echo 尝试安装luasocket...
echo 注意: 如果没有网络连接，可能会失败

if exist "%LUAROCKS_DIR%\binary\luasocket-3.1.0-1.rockspec" (
    echo 使用本地rockspec文件...
    "%LUA_DIR%\bin\lua5.1.exe" "%LUAROCKS_DIR%\src\bin\luarocks" install "%LUAROCKS_DIR%\binary\luasocket-3.1.0-1.rockspec" --tree="rocks"
) else (
    echo 从网络安装luasocket...
    "%LUA_DIR%\bin\lua5.1.exe" "%LUAROCKS_DIR%\src\bin\luarocks" install luasocket --tree="rocks"
)

echo 安装lfs...
"%LUA_DIR%\bin\lua5.1.exe" "%LUAROCKS_DIR%\src\bin\luarocks" install luafilesystem --tree="rocks"

echo 安装完成!
echo.
echo 已安装的包:
"%LUA_DIR%\bin\lua5.1.exe" "%LUAROCKS_DIR%\src\bin\luarocks" list --tree="rocks"

pause
