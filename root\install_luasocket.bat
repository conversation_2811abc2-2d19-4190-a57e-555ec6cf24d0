@echo off
echo 安装luasocket到本地luarocks环境...

REM 设置路径
set LUAROCKS_DIR=%~dp0..\luarocks-3.12.2-win32
set LUA_DIR=%LUAROCKS_DIR%\win32\lua5.1
set PATH=%LUA_DIR%\bin;%LUAROCKS_DIR%\src\bin;%PATH%

REM 创建本地rocks目录
if not exist "%~dp0rocks" mkdir "%~dp0rocks"
if not exist "%~dp0rocks\lib" mkdir "%~dp0rocks\lib"
if not exist "%~dp0rocks\share" mkdir "%~dp0rocks\share"

echo 当前目录: %~dp0
echo Lua目录: %LUA_DIR%
echo LuaRocks目录: %LUAROCKS_DIR%

REM 检查lua是否可用
echo 检查Lua安装...
"%LUA_DIR%\bin\lua5.1.exe" -v
if errorlevel 1 (
    echo 错误: 无法找到lua5.1.exe
    pause
    exit /b 1
)

REM 检查luarocks是否可用
echo 检查LuaRocks安装...
"%LUAROCKS_DIR%\src\bin\luarocks.bat" --version
if errorlevel 1 (
    echo 错误: 无法找到luarocks
    pause
    exit /b 1
)

REM 安装luasocket
echo 安装luasocket...
"%LUAROCKS_DIR%\src\bin\luarocks.bat" install luasocket --tree="%~dp0rocks"

REM 安装lfs
echo 安装lfs...
"%LUAROCKS_DIR%\src\bin\luarocks.bat" install luafilesystem --tree="%~dp0rocks"

echo 安装完成!
echo.
echo 已安装的包:
"%LUAROCKS_DIR%\src\bin\luarocks.bat" list --tree="%~dp0rocks"

pause
