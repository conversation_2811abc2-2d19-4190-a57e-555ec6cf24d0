@echo off
echo 简单测试脚本
echo.

echo 当前目录: %cd%
echo.

echo 检查目录结构...
if exist "root" (
    echo [OK] root目录存在
) else (
    echo [ERROR] root目录不存在
)

if exist "luarocks-3.12.2-win32" (
    echo [OK] luarocks目录存在
) else (
    echo [ERROR] luarocks目录不存在
)

if exist "sc" (
    echo [OK] sc目录存在
) else (
    echo [ERROR] sc目录不存在
)

echo.
echo 检查Lua可执行文件...
set LUA_EXE=luarocks-3.12.2-win32\win32\lua5.1\bin\lua5.1.exe
if exist "%LUA_EXE%" (
    echo [OK] 找到lua5.1.exe
    echo 路径: %LUA_EXE%
) else (
    echo [ERROR] 找不到lua5.1.exe
    echo 期望路径: %LUA_EXE%
)

echo.
echo 测试完成
echo 按任意键继续...
pause >nul
