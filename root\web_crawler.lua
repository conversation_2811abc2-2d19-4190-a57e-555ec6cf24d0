-- 网络爬虫主程序
-- 支持多种协议和数据格式

local crawler = {}

-- 配置
local config = {
    user_agent = "LuaCrawler/1.0",
    timeout = 10,
    max_redirects = 5,
    delay_between_requests = 1, -- 秒
    output_dir = "crawled_data",
    log_file = "crawler.log"
}

-- 日志函数
local function log(message)
    local timestamp = os.date("%Y-%m-%d %H:%M:%S")
    local log_entry = string.format("[%s] %s\n", timestamp, message)
    print(log_entry:sub(1, -2)) -- 打印到控制台（去掉换行符）
    
    -- 写入日志文件
    local file = io.open(config.log_file, "a")
    if file then
        file:write(log_entry)
        file:close()
    end
end

-- URL解析函数
local function parse_url(url)
    local protocol, host, port, path = url:match("^(https?)://([^:/]+):?(%d*)(.*)$")
    if not protocol then
        return nil, "无效的URL格式"
    end
    
    port = port and tonumber(port) or (protocol == "https" and 443 or 80)
    path = path == "" and "/" or path
    
    return {
        protocol = protocol,
        host = host,
        port = port,
        path = path,
        full_url = url
    }
end

-- HTTP请求构建函数
local function build_http_request(url_info, method)
    method = method or "GET"
    local request = string.format("%s %s HTTP/1.1\r\n", method, url_info.path)
    request = request .. string.format("Host: %s\r\n", url_info.host)
    request = request .. string.format("User-Agent: %s\r\n", config.user_agent)
    request = request .. "Connection: close\r\n"
    request = request .. "\r\n"
    return request
end

-- 简单的HTTP响应解析
local function parse_http_response(response)
    if not response then
        return nil, "空响应"
    end
    
    local header_end = response:find("\r\n\r\n")
    if not header_end then
        return nil, "无效的HTTP响应"
    end
    
    local headers = response:sub(1, header_end - 1)
    local body = response:sub(header_end + 4)
    
    -- 解析状态行
    local status_line = headers:match("^[^\r\n]+")
    local status_code = status_line and status_line:match("HTTP/1%.[01] (%d+)")
    
    return {
        status_code = tonumber(status_code),
        headers = headers,
        body = body,
        full_response = response
    }
end

-- 网络请求函数（使用socket或模拟）
local function make_request(url)
    log("开始请求: " .. url)
    
    local url_info, err = parse_url(url)
    if not url_info then
        log("URL解析失败: " .. err)
        return nil, err
    end
    
    -- 尝试加载socket
    local socket
    local success, luasocket = pcall(require, "socket")
    if success then
        socket = luasocket
        log("使用真实socket连接")
    else
        socket = require("simple_socket")
        log("使用模拟socket连接")
    end
    
    local tcp = socket.tcp()
    if tcp.settimeout then
        tcp:settimeout(config.timeout)
    end
    
    -- 连接到服务器
    local result, err = tcp:connect(url_info.host, url_info.port)
    if not result then
        log("连接失败: " .. (err or "未知错误"))
        return nil, "连接失败: " .. (err or "未知错误")
    end
    
    log("连接成功，发送HTTP请求")
    
    -- 发送HTTP请求
    local request = build_http_request(url_info)
    local bytes_sent, err = tcp:send(request)
    if not bytes_sent then
        log("发送请求失败: " .. (err or "未知错误"))
        tcp:close()
        return nil, "发送请求失败"
    end
    
    log("请求已发送，等待响应")
    
    -- 接收响应
    local response = ""
    while true do
        local data, err = tcp:receive("*a")
        if data then
            response = response .. data
        end
        if err == "closed" or not data or data == "" then
            break
        end
    end
    
    tcp:close()
    log("响应接收完成，长度: " .. #response)
    
    return parse_http_response(response)
end

-- 保存数据到文件
local function save_data(filename, data)
    -- 创建输出目录
    os.execute("mkdir " .. config.output_dir .. " 2>nul")
    
    local filepath = config.output_dir .. "/" .. filename
    local file = io.open(filepath, "w")
    if file then
        file:write(data)
        file:close()
        log("数据已保存到: " .. filepath)
        return true
    else
        log("保存文件失败: " .. filepath)
        return false
    end
end

-- 主爬虫函数
function crawler.crawl(url, options)
    options = options or {}
    
    log("开始爬取: " .. url)
    
    local response, err = make_request(url)
    if not response then
        log("请求失败: " .. err)
        return nil, err
    end
    
    log("HTTP状态码: " .. (response.status_code or "未知"))
    
    if response.status_code and response.status_code == 200 then
        log("请求成功，处理响应数据")
        
        -- 生成文件名
        local filename = url:gsub("[^%w%-_.]", "_") .. ".html"
        
        -- 保存原始响应
        if options.save_raw then
            save_data(filename, response.full_response)
        end
        
        -- 保存处理后的内容
        if options.save_body then
            local body_filename = filename:gsub("%.html$", "_body.html")
            save_data(body_filename, response.body)
        end
        
        return response
    else
        local error_msg = "HTTP错误: " .. (response.status_code or "未知状态码")
        log(error_msg)
        return nil, error_msg
    end
end

-- 批量爬取函数
function crawler.crawl_multiple(urls, options)
    options = options or {}
    local results = {}
    
    log("开始批量爬取，共 " .. #urls .. " 个URL")
    
    for i, url in ipairs(urls) do
        log("处理第 " .. i .. "/" .. #urls .. " 个URL")
        
        local response, err = crawler.crawl(url, options)
        results[i] = {
            url = url,
            response = response,
            error = err,
            success = response ~= nil
        }
        
        -- 请求间延迟
        if i < #urls and config.delay_between_requests > 0 then
            log("等待 " .. config.delay_between_requests .. " 秒...")
            os.execute("timeout " .. config.delay_between_requests .. " >nul 2>&1")
        end
    end
    
    log("批量爬取完成")
    return results
end

return crawler
