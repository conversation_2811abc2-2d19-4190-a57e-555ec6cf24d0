@echo off
echo Downloading LuaSocket for Lua 5.1...

REM Create directory
mkdir luasocket_files 2>nul

REM Download using wget from luarocks tools
echo Using wget to download...
luarocks-3.12.2-win32\win32\tools\wget.exe -O luasocket_files\luasocket.zip "https://www.unrealsoftware.de/get.php?get=u28e0_551c4506.zip&p=2"

REM Check if download was successful
if exist luasocket_files\luasocket.zip (
    echo Download successful!
    echo Extracting files...
    
    REM Extract using 7z
    luarocks-3.12.2-win32\win32\tools\7z.exe x luasocket_files\luasocket.zip -oluasocket_files\
    
    echo Files extracted to luasocket_files\
    dir luasocket_files
) else (
    echo Download failed!
)

pause
