-- SC脚本适配器
-- 为现有的sc脚本提供兼容的API

local adapter = {}

-- 全局变量，用于与mud_client通信
_G.mud_client_instance = nil

-- 模拟ZMUD/CMUD的API函数
function Echo(text)
    print("[ECHO] " .. tostring(text))
end

function Print(text)
    print("[PRINT] " .. tostring(text))
end

function Send(cmd)
    if _G.mud_client_instance and _G.mud_client_instance.send then
        return _G.mud_client_instance.send(cmd)
    else
        print("[SEND] " .. tostring(cmd))
        return false
    end
end

function IsConnect()
    if _G.mud_client_instance then
        return _G.mud_client_instance.connected or false
    end
    return false
end

function Connect()
    if _G.mud_client_instance and _G.mud_client_instance.connect then
        return _G.mud_client_instance.connect()
    end
    return false
end

function Disconnect()
    if _G.mud_client_instance and _G.mud_client_instance.disconnect then
        _G.mud_client_instance.disconnect()
    end
end

function GameBytes()
    -- 返回接收到的字节数，这里简单返回0
    return 0
end

-- 变量存储系统
local variables = {}

function GetVar(name)
    return variables[name]
end

function SetVar(name, value)
    variables[name] = value
end

-- 定时器系统
local timers = {}
local timer_id_counter = 0

function AddTimer(name, command, delay_ms, repeat_count)
    timer_id_counter = timer_id_counter + 1
    local timer_id = timer_id_counter
    
    timers[name] = {
        id = timer_id,
        command = command,
        delay = delay_ms / 1000, -- 转换为秒
        repeat_count = repeat_count or 1,
        current_count = 0
    }
    
    -- 这里需要实际的定时器实现
    -- 可以使用socket.sleep在单独的协程中实现
    print("[TIMER] 添加定时器: " .. name .. ", 延迟: " .. delay_ms .. "ms")
    
    return timer_id
end

function DelTimer(name)
    if timers[name] then
        timers[name] = nil
        print("[TIMER] 删除定时器: " .. name)
        return true
    end
    return false
end

-- 触发器系统
trig_rex = {}
trig_func = {}

function reset_trigger()
    -- 重置触发器状态，防止在遍历过程中修改表
    -- 这里可以添加具体的重置逻辑
end

-- 别名系统
alias = {}

function add_alias(name, func)
    alias[name] = func
end

-- 正则表达式支持
rex = {}

function rex.match(text, pattern)
    -- 简单的正则匹配实现
    -- 这里需要根据实际需要实现更复杂的正则匹配
    local matches = {string.match(text, pattern)}
    if #matches > 0 then
        return unpack(matches)
    end
    return nil
end

-- 文件系统支持
local lfs_ok, lfs = pcall(require, "lfs")
if lfs_ok then
    -- lfs已经可用
else
    -- 提供简单的lfs模拟
    lfs = {}
    function lfs.currentdir()
        return "."
    end
end

-- 字符串处理函数
function expand(text)
    -- 展开变量和特殊字符
    if not text then return "" end
    
    -- 简单的变量替换实现
    text = string.gsub(text, "%$(%w+)%$", function(var_name)
        return variables[var_name] or ("$" .. var_name .. "$")
    end)
    
    return text
end

-- 工具函数
function num_item(str, delimiter)
    if not str or str == "" then return 0 end
    local count = 1
    for _ in string.gmatch(str, delimiter) do
        count = count + 1
    end
    return count
end

function wait(delay, func)
    -- 简单的等待实现
    if func then
        -- 这里应该使用定时器来延迟执行函数
        print("[WAIT] 延迟 " .. delay .. " 秒执行函数")
        -- 暂时直接执行
        func()
    end
end

-- 初始化适配器
function adapter.init(client_instance)
    _G.mud_client_instance = client_instance
    print("[ADAPTER] SC脚本适配器已初始化")
end

-- 处理定时器
function adapter.process_timers()
    -- 这里应该处理所有活动的定时器
    -- 可以在主循环中调用
end

return adapter
