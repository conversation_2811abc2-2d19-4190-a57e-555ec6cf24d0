# Index

* [Welcome](welcome.md)

* Installing LuaRocks
  * [Download](download.md)
    * [Installation instructions for Unix](installation_instructions_for_unix.md)
    * [Installation instructions for macOS](installation_instructions_for_macos.md)
    * [Installation instructions for Windows](installation_instructions_for_windows.md)

* Using LuaRocks
  * [Using LuaRocks](using_luarocks.md)
  * [Types of rocks](types_of_rocks.md)
  * [Rocks repositories](rocks_repositories.md)
  * [Browse rocks](browse_rocks.md)
  * [Dependencies](dependencies.md)
    * [Paths and external dependencies](paths_and_external_dependencies.md)
    * [Namespaces](namespaces.md)
    * [Pinning versions with a lock file](pinning_versions_with_a_lock_file.md)
  * [Community support and more resources](more_resources.md)

* Creating rocks
  * [Creating a rock](creating_a_rock.md)
  * [Platform overrides](platform_overrides.md)
  * [Platform-agnostic external dependencies](platform_agnostic_external_dependencies.md)
  * [Recommended practices for Makefiles](recommended_practices_for_makefiles.md)
    * [Creating a Makefile that plays nice with LuaRocks](creating_a_makefile_that_plays_nice_with_luarocks.md)
  * [Creating LuaRocks with GNU autotools](creating_luarocks_with_gnu_autotools.md)

* Advanced topics
  * [LuaRocks through a proxy](luarocks_through_a_proxy.md)
  * [Embedding LuaRocks in an application](embedding_luarocks_in_an_application.md)
  * [Integrating distro modules with LuaRocks](integrating_distro_modules_with_luarocks.md)

* LuaRocks servers
  * [Creating a local LuaRocks server](local_server.md)
  * [Hosting binary rocks](hosting_binary_rocks.md)

* Reference
  * [File formats](file_formats.md)
    * [Config file format](config_file_format.md)
    * [Rock file format](rock_file_format.md)
    * [Rockspec format](rockspec_format.md)
    * [Rock manifest file format](rock_manifest_file_format.md)
    * [Manifest file format](manifest_file_format.md)

  * [File locations](file_locations.md)

  * Command-line interface
    * [luarocks](luarocks.md)
      * [luarocks build](luarocks_build.md)
      * [luarocks config](luarocks_config.md)
      * [luarocks doc](luarocks_doc.md)
      * [luarocks download](luarocks_download.md)
      * [luarocks help](luarocks_help.md)
      * [luarocks install](luarocks_install.md)
      * [luarocks lint](luarocks_lint.md)
      * [luarocks list](luarocks_list.md)
      * [luarocks make](luarocks_make.md)
      * [luarocks new_version](luarocks_new_version.md)
      * [luarocks pack](luarocks_pack.md)
      * [luarocks path](luarocks_path.md)
      * [luarocks purge](luarocks_purge.md)
      * [luarocks remove](luarocks_remove.md)
      * [luarocks search](luarocks_search.md)
      * [luarocks show](luarocks_show.md)
      * [luarocks test](luarocks_test.md)
      * [luarocks unpack](luarocks_unpack.md)
      * [luarocks upload](luarocks_upload.md)
      * [luarocks write_rockspec](luarocks_write_rockspec.md)

    * [luarocks-admin](luarocks_admin.md)
      * [luarocks-admin add](luarocks_admin_add.md)
      * [luarocks-admin help](luarocks_admin_help.md)
      * [luarocks-admin make manifest](luarocks_admin_make_manifest.md)
      * [luarocks-admin refresh cache](luarocks_admin_refresh_cache.md)
      * [luarocks-admin remove](luarocks_admin_remove.md)

  * [Release history](../CHANGELOG.md)

* [Credits](credits.md)
* [License](license.md)
* [Index](index.md)
