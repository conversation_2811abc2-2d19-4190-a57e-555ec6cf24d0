-- MUD客户端配置文件
-- 使用luarocks和luasocket连接MUD服务器

-- 设置lua路径，指向luarocks中的lua5.1
local luarocks_path = "luarocks-3.12.2-win32/win32/lua5.1"
local lua_exe = luarocks_path .. "/bin/lua5.1.exe"
local lua_dll = luarocks_path .. "/bin/lua51.dll"

-- 配置包路径
package.path = package.path .. ";./sc/?.lua;./root/?.lua"
package.cpath = package.cpath .. ";./luarocks-3.12.2-win32/win32/lua5.1/bin/?.dll"

-- MUD服务器配置
local mud_config = {
    -- 默认服务器配置
    default_server = {
        host = "localhost",
        port = 4000,
        encoding = "gbk"  -- 中文MUD通常使用gbk编码
    },
    
    -- 可以添加多个服务器配置
    servers = {
        ["pkuxkx"] = {
            host = "mud.pkuxkx.net",
            port = 8080,
            encoding = "gbk"
        },
        ["nt"] = {
            host = "nt.mud.ren",
            port = 8888,
            encoding = "gbk"
        }
    },
    
    -- 连接设置
    connection = {
        timeout = 30,           -- 连接超时时间(秒)
        keepalive = true,       -- 保持连接
        reconnect_delay = 5,    -- 重连延迟(秒)
        max_reconnect = 10      -- 最大重连次数
    },
    
    -- 脚本设置
    script = {
        auto_load = true,       -- 自动加载脚本
        script_dir = "./sc",    -- 脚本目录
        main_script = "main.lua" -- 主脚本文件
    }
}

return mud_config
