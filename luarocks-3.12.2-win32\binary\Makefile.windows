
# "i686-w64-mingw32" or "x86_64-w64-mingw32"
MINGW_PREFIX?=i686-w64-mingw32
# sysroot of your mingw-w64 installation
MINGW_SYSROOT=/usr/lib/mingw-w64-sysroot/$(MINGW_PREFIX)
# "mingw" or "mingw64"
OPENSSL_PLATFORM=mingw
# Versions of dependencies
LIBLUA_VERSION=5.4.3
OPENSSL_VERSION=1.1.1w
ZLIB_VERSION=1.3.1
BZIP2_VERSION=1.0.8

WINDOWS_DEPS_DIR=windows-deps-$(MINGW_PREFIX)
BUILD_WINDOWS_DEPS_DIR=build-windows-deps-$(MINGW_PREFIX)
BUILD_WINDOWS_BINARY_DIR=build-windows-binary-$(MINGW_PREFIX)

windows-binary: $(WINDOWS_DEPS_DIR)/lib/liblua.a $(WINDOWS_DEPS_DIR)/lib/libssl.a $(WINDOWS_DEPS_DIR)/lib/libz.a $(WINDOWS_DEPS_DIR)/lib/libbz2.a
	STATIC_GCC_AR=$(MINGW_PREFIX)-ar \
	STATIC_GCC_RANLIB=$(MINGW_PREFIX)-ranlib \
	STATIC_GCC_CC=$(MINGW_PREFIX)-gcc \
	LUAROCKS_CROSS_COMPILING=1 \
	$(MAKE) binary LUA_DIR=$(CURDIR)/$(WINDOWS_DEPS_DIR) CC=$(MINGW_PREFIX)-gcc NM=$(MINGW_PREFIX)-nm BINARY_PLATFORM=windows buildbinarydir=$(BUILD_WINDOWS_BINARY_DIR) BINARY_SYSROOT=$(MINGW_SYSROOT)

$(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION).tar.gz:
	mkdir -p $(@D)
	cd $(BUILD_WINDOWS_DEPS_DIR) && curl -OL https://www.lua.org/ftp/lua-$(LIBLUA_VERSION).tar.gz
$(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION): $(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION).tar.gz
	cd $(BUILD_WINDOWS_DEPS_DIR) && tar zxvpf lua-$(LIBLUA_VERSION).tar.gz
$(WINDOWS_DEPS_DIR)/lib/liblua.a: $(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION)
	$(MAKE) -C "$(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION)/src" LUA_A=liblua.a CC=$(MINGW_PREFIX)-gcc AR="$(MINGW_PREFIX)-ar rcu" RANLIB=$(MINGW_PREFIX)-ranlib SYSCFLAGS= SYSLIBS= SYSLDFLAGS= liblua.a
	mkdir -p $(WINDOWS_DEPS_DIR)/include
	cd $(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION)/src && cp lauxlib.h lua.h lua.hpp luaconf.h lualib.h ../../../$(WINDOWS_DEPS_DIR)/include
	mkdir -p $(WINDOWS_DEPS_DIR)/lib
	cd $(BUILD_WINDOWS_DEPS_DIR)/lua-$(LIBLUA_VERSION)/src && cp liblua.a ../../../$(WINDOWS_DEPS_DIR)/lib

$(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION).tar.gz:
	mkdir -p $(@D)
	cd $(BUILD_WINDOWS_DEPS_DIR) && curl -OL https://www.openssl.org/source/openssl-$(OPENSSL_VERSION).tar.gz
$(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION): $(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION).tar.gz
	cd $(BUILD_WINDOWS_DEPS_DIR) && tar zxvpf openssl-$(OPENSSL_VERSION).tar.gz
$(WINDOWS_DEPS_DIR)/lib/libssl.a: $(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION)
	cd $(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION) && ./Configure --prefix=$(CURDIR)/$(WINDOWS_DEPS_DIR) --cross-compile-prefix=$(MINGW_PREFIX)- $(OPENSSL_PLATFORM)
	$(MAKE) -C "$(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION)"
	$(MAKE) -C "$(BUILD_WINDOWS_DEPS_DIR)/openssl-$(OPENSSL_VERSION)" install_sw

$(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION).tar.gz:
	mkdir -p $(@D)
	cd $(BUILD_WINDOWS_DEPS_DIR) && curl -OL https://www.zlib.net/zlib-$(ZLIB_VERSION).tar.gz
$(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION): $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION).tar.gz
	cd $(BUILD_WINDOWS_DEPS_DIR) && tar zxvpf zlib-$(ZLIB_VERSION).tar.gz
$(WINDOWS_DEPS_DIR)/lib/libz.a: $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION)
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && sed -ie "s,dllwrap,$(MINGW_PREFIX)-dllwrap," win32/Makefile.gcc
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && ./configure --prefix=$(CURDIR)/$(WINDOWS_DEPS_DIR) --static
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && $(MAKE) -f win32/Makefile.gcc CC=$(MINGW_PREFIX)-gcc AR=$(MINGW_PREFIX)-ar RC=$(MINGW_PREFIX)-windres STRIP=$(MINGW_PREFIX)-strip IMPLIB=libz.dll.a
	mkdir -p $(WINDOWS_DEPS_DIR)/include
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && cp zlib.h zconf.h ../../$(WINDOWS_DEPS_DIR)/include
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && $(MINGW_PREFIX)-strip -g libz.a
	mkdir -p $(@D)
	cd $(BUILD_WINDOWS_DEPS_DIR)/zlib-$(ZLIB_VERSION) && cp libz.a ../../$(WINDOWS_DEPS_DIR)/lib

$(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION).tar.gz:
	mkdir -p $(@D)
	cd $(BUILD_WINDOWS_DEPS_DIR) && curl -OL https://sourceware.org/pub/bzip2/bzip2-$(BZIP2_VERSION).tar.gz
$(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION): $(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION).tar.gz
	cd $(BUILD_WINDOWS_DEPS_DIR) && tar zxvpf bzip2-$(BZIP2_VERSION).tar.gz
$(WINDOWS_DEPS_DIR)/lib/libbz2.a: $(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION)
	$(MAKE) -C "$(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION)" libbz2.a CC=$(MINGW_PREFIX)-gcc AR=$(MINGW_PREFIX)-ar RANLIB=$(MINGW_PREFIX)-ranlib
	mkdir -p $(WINDOWS_DEPS_DIR)/include
	cd $(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION) && cp bzlib.h ../../$(WINDOWS_DEPS_DIR)/include
	cd $(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION) && $(MINGW_PREFIX)-strip -g libbz2.a
	mkdir -p $(WINDOWS_DEPS_DIR)/lib
	cd $(BUILD_WINDOWS_DEPS_DIR)/bzip2-$(BZIP2_VERSION) && cp libbz2.a ../../$(WINDOWS_DEPS_DIR)/lib

windows-clean:
	rm -rf $(WINDOWS_DEPS_DIR) $(BUILD_WINDOWS_BINARY_DIR)
