-- Test if luasocket is available

print("Testing luasocket availability...")

-- Try different ways to load luasocket
local methods = {
    function() return require("socket") end,
    function() return require("socket.core") end,
    function() return require("luasocket") end,
}

local socket = nil
local method_used = nil

for i, method in ipairs(methods) do
    local success, result = pcall(method)
    if success then
        socket = result
        method_used = i
        print("✅ LuaSocket loaded successfully using method " .. i)
        break
    else
        print("❌ Method " .. i .. " failed: " .. tostring(result))
    end
end

if socket then
    print("LuaSocket version: " .. (socket._VERSION or "unknown"))
    print("Available functions:")
    for k, v in pairs(socket) do
        if type(v) == "function" then
            print("  " .. k .. "()")
        end
    end
    
    -- Test creating a TCP socket
    local success, tcp = pcall(socket.tcp)
    if success then
        print("✅ TCP socket creation successful")
        tcp:close()
    else
        print("❌ TCP socket creation failed: " .. tostring(tcp))
    end
else
    print("❌ LuaSocket not available")
    print("Checking Lua package path:")
    print("package.path = " .. package.path)
    print("package.cpath = " .. package.cpath)
end
