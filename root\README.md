# CFAN MUD 客户端

这是一个基于Lua 5.1和LuaSocket的MUD客户端，用于连接MUD服务器并运行sc目录下的脚本。

## 目录结构

```
cfanmud/
├── root/                    # 配置和运行环境
│   ├── config.lua          # 服务器配置文件
│   ├── mud_client.lua      # MUD客户端主程序
│   ├── start.lua           # 启动脚本
│   ├── install_luasocket.bat # 安装依赖脚本
│   ├── run.bat             # 运行脚本
│   └── rocks/              # 本地rocks安装目录(运行后生成)
├── sc/                     # 游戏脚本目录
│   ├── main.lua           # 主脚本文件
│   └── ...                # 其他脚本文件
└── luarocks-3.12.2-win32/ # LuaRocks和Lua 5.1环境
    └── win32/lua5.1/      # Lua 5.1 32位版本
```

## 安装和使用

### 1. 安装依赖

首先运行安装脚本来安装luasocket和其他必要的依赖：

```bash
cd root
install_luasocket.bat
```

这将会：
- 在root目录下创建rocks文件夹
- 安装luasocket到本地rocks环境
- 安装luafilesystem(lfs)

### 2. 配置服务器

编辑 `root/config.lua` 文件，配置你要连接的MUD服务器：

```lua
servers = {
    ["your_mud"] = {
        host = "your.mud.server.com",
        port = 4000,
        encoding = "gbk"
    }
}
```

### 3. 运行客户端

```bash
cd root
run.bat
```

或者直接双击 `root/run.bat` 文件。

## 使用说明

启动客户端后，你可以使用以下命令：

- `connect <server_name>` - 连接到指定服务器
- `connect default_server` - 连接到默认服务器
- `disconnect` - 断开连接
- `quit` - 退出客户端

连接成功后，你输入的所有命令都会发送到MUD服务器，服务器的响应会显示在屏幕上。

## 脚本集成

客户端会自动加载 `sc/main.lua` 脚本。你可以在脚本中定义以下函数：

- `OnReceive(raw, txt)` - 处理从服务器接收到的数据
- `OnSend(cmd)` - 处理发送到服务器的命令

## 故障排除

### 1. luasocket安装失败

如果安装luasocket失败，请检查：
- 确保luarocks-3.12.2-win32目录存在
- 确保有网络连接
- 尝试手动运行luarocks命令

### 2. 连接失败

如果无法连接到MUD服务器，请检查：
- 服务器地址和端口是否正确
- 网络连接是否正常
- 防火墙设置

### 3. 脚本加载失败

如果脚本加载失败，请检查：
- sc/main.lua文件是否存在
- 脚本语法是否正确
- 文件编码是否为UTF-8

## 技术细节

- 使用Lua 5.1 32位版本
- 使用LuaSocket进行网络通信
- 支持多服务器配置
- 支持编码转换(需要进一步实现)
- 集成现有的sc脚本系统
