# Installation instructions for macOS

LuaRocks is a command-line tool. You can install LuaRocks using one of the
third-party package managers available for macOS (formerly known as "Mac OS X"
or "OS X"), or you may install it manually, in the Unix way.

# Option 1: using Homebrew 

One option is to use [Homebrew](https://brew.sh). If you don't have it
installed, follow its installation instructions.

If you already have it installed, make sure it is up to date so you pick the
most recent version of LuaRocks:

```
brew update
```

and then install Lua<PERSON><PERSON>:

```
  brew install luarocks
```

# Option 2: installing it manually 

If you do not wish to use a package management tool to install LuaRocks, you
can install it on macOS as you would do in any other Unix system, assuming you
have the Mac Developer Tools installed. Download the Unix tarball and then
follow the [installation instructions for
Unix](installation_instructions_for_unix.md) to install it to /usr/local.

# Next steps 

Once Lua<PERSON>ocks is installed, learn more about [using
Lua<PERSON>ocks](using_luarocks.md).



