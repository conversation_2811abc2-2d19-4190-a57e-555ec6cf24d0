@echo off
echo Installing LuaSocket manually...

REM Create directories
mkdir luasocket 2>nul
cd luasocket

echo Downloading LuaSocket...
REM Download luasocket source
curl -L -o luasocket-3.1.0.tar.gz https://github.com/lunarmodules/luasocket/archive/refs/tags/v3.1.0.tar.gz

REM If curl fails, try wget
if not exist luasocket-3.1.0.tar.gz (
    echo Trying wget...
    ..\luarocks-3.12.2-win32\win32\tools\wget.exe -O luasocket-3.1.0.tar.gz https://github.com/lunarmodules/luasocket/archive/refs/tags/v3.1.0.tar.gz
)

REM Extract
if exist luasocket-3.1.0.tar.gz (
    echo Extracting...
    ..\luarocks-3.12.2-win32\win32\tools\7z.exe x luasocket-3.1.0.tar.gz
    ..\luarocks-3.12.2-win32\win32\tools\7z.exe x luasocket-3.1.0.tar
    
    echo LuaSocket downloaded and extracted
    dir
) else (
    echo Failed to download LuaSocket
)

cd ..
pause
