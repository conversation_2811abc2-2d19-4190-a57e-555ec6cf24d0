--�������������
C = {}      --��ɫ
C.d = "$CYN$" --BLK��һ�� C.d="$BLK$"
C.r = "$RED$"
C.g = "$GRN$"
C.y = "$YEL$"
C.b = "$BLU$"
C.m = "$MAG$"
C.c = "$HIC$" --CYN��һ�� C.c="$CYN$"
C.x = "$WHT$"
C.D = "$NOR$"
C.R = "$HIR$"
C.G = "$HIG$"
C.Y = "$HIY$"
C.B = "$HIB$"
C.M = "$HIM$"
C.C = "$HIC$"
C.W = "$HIW$"
C.k = "$BNK$" --��˸
C.u = "$U$" --�»���
C.v = "$REV$" --��ɫ

mb = {}     --����mudbot mb

function echo(s)
	return Echo(s) --��ʾ�����棬���Դ���
end

function send(s) --send
	if var.debug and var.debug > 0 then
		Print(s) --��ʾ��������
	end
	return Send(s) --���͸�������
end

save_timer_actions = {}        --�������ж�ʱ��,��ʽid=function()

function call_timer_actions(s) --������ִ��timer��ָ��� %%call_timer_actions(wait)
	if s ~= nil and s ~= "" and save_timer_actions[s] then
		return save_timer_actions[s]()
	end
end

function killalltimer() --ɱ������timer
	Print("Timer:������ж�ʱ��")
	DelTimer("wait")
	DelTimer("wait1")
	DelTimer("wait2")
	DelTimer("timer")
	DelTimer("timer2")
	DelTimer("alarm")
	DelTimer("check_busy_1")
	DelTimer("check_busy_2")
	DelTimer("check_busy")
	DelTimer("idle")
	DelTimer("checkidle_mods")
	DelTimer("input")
	save_timer_actions = {}
end

function add_timer(delay, f, id) --һ����timer
	save_timer_actions[id] = f -- �������timer��action
	delay = math.ceil(delay * 1000) --ʱ��ת��Ϊ����
	return AddTimer(id, "%%call_timer_actions(" .. id .. ")", delay, 1)
end

function del_timer(id) --ɾ��timer
	save_timer_actions[id] = nil
	return DelTimer(id)
end

function get_receive() --�Լ���������������ֽ��������ص�ǰ�ֽ�
	return GameBytes()
end

function process_client_line() --ԭ��zmud lua����
	if IsConnect() then
		Disconnect()
		Connect()
	else
		Connect()
	end
end

line = {}
raw = {}
rawline = ""
function getrawline()
	return rawline or ""
end

function OnReceive(raw, txt)            --����������Ϣ����
	--              ԭʼ��ɫ��Ϣ  һ����Ϣ
	--	Print(raw)
	local from_server_line = tostring(txt) --�ѷ�������Ϣת��string
	table.insert(line, 1, from_server_line)
	line[10001] = nil --���Լ�¼10000��
	rawline = tostring(raw)
	--> > > > > > ����
	from_server_line = string.gsub(from_server_line, "> ", "")
	reset_trigger() --��������trigger�����ر�״̬ ��ֹ����table���̱�������ʹ��pairsʱ��table�����ݱ仯���ܵ��±���
	--ע������open��close ��del��������һ�в��ܴ��������в�����
	for id, _ in pairs(trig_rex) do
		local params = {}
		params[1], params[2], params[3], params[4], params[5], params[6], params[7], params[8], params[9] = rex.match(
		from_server_line, trig_rex[id])                                                                                              --����1-9��params[1] -- params[9]

		if params[1] then                                                                                                            --ƥ��ɹ�
			params[-1] = from_server_line                                                                                            --����-1��params[-1]��������
			trig_func[id](params)
		end
	end
	--��Ҫ��ɫ������ͬѧ�Լ�����rawдһ���ѣ�����ֱ��дlordstar����
	--[2;37;0m[1;37m�������������[2;37;0m[1;32m(Wjyou)[2;37;0m[1;37m: find kodase[2;37;0m
end

function OnSend(cmd)
	cmd = tostring(cmd)
	if var["debug"] and var["debug"] > 0 then
		Print(cmd)
	end
	--	Print(cmd)
	for match in string.gmatch(cmd .. ";", "(.-);") do
		if string.match(match, "(#%d+)%s-.+") then
			local alias_times = string.match(match, "#(%d+)%s.+")
			local alias_action = string.match(match, "#%d+%s(.+)")
			for i = 1, times do
				if string.match(alias_action, "(%S+)") and string.match(alias_action, "(%S+)") ~= "" and alias[string.match(alias_action, "(%S+)")] then
					local i = 0
					local params = { "", "", "", "", "", "", "", "", "", "" }
					for match in string.gmatch(alias_action, "(%S+)") do
						params[i] = expand(match)
						i = i + 1
					end

					if alias[params[0]] then
						params[-1] = string.match(alias_action, "^" .. params[0] .. " (.+)")
						if params[-1] then params[-1] = expand(params[-1]) end
						alias[params[0]](params)
						return false
					end
				end
			end
		else
			if string.match(match, "(%S+)") and string.match(match, "(%S+)") ~= "" and alias[string.match(match, "(%S+)")] then
				local i = 0
				local matchs
				local params = { "", "", "", "", "", "", "", "", "", "" }
				for matchs in string.gmatch(match, "(%S+)") do
					params[i] = expand(matchs)
					i = i + 1
				end

				if alias[params[0]] then
					params[-1] = string.match(match, "^" .. params[0] .. " (.+)")
					if params[-1] then params[-1] = expand(params[-1]) end
					alias[params[0]](params)
					return false
				end
			end
		end
	end
end
function loadsetting() --����һ�������ķ�	
	local fileicludes = nil
	local fileconfig = nil
	local filepasswd = nil
	local filepath = nil
	local path
	local id = GetVar('id')
	var = var or {}
	var["walk_wait"] = var["walk_wait"] or 1
	var["walk_step"] = var["walk_step"] or 12
	local success, lfs = pcall(require, "lfs")
    if not success then
        Echo("$HIR$��������lfs�����ʧ�ܣ�����lfs.dll�Ƿ�����ȷ���á�")
        Echo("$HIR$��������: " .. tostring(lfs))
        return 
    end    
    local path = lfs.currentdir()    
    if not string.find(path, "\\$") then
        path = path .. "\\"
    end    
	var.filepath = path	
	if id ~= nil and id ~= "" then
		local passwd = GetVar('passwd') or ""                 --��������
		if passwd == "" then
			filepasswd = io.open(var.filepath .. id .. "\\" .. id .. ".txt", "r") --�ļ����ҳ�
			if filepasswd ~= nil then
				passwd = filepasswd:read()
				filepasswd:close()
			end
		end		
		if passwd ~= nil and passwd ~= "" and passwd ~= " " then
			var.passwd = passwd
			fileconfig = io.open(var.filepath .. id .. "\\my-config.txt")
			if fileconfig ~= nil then
				fileconfig:close()
				for line in io.lines(var.filepath .. id .. "\\my-config.txt") do
					
					local m1, m2 = string.match(line, "^var%[(.*)%]%=(.*)")
					if m1 then						
						m1 = string.gsub(m1, '%"', "")
						m2 = string.gsub(m2, '%"', "")
						local m3 = tonumber(m2)
						if m3 == nil then
							var[m1] = m2
					--		Print('var["' .. m1 .. '"]="' .. m2 .. '"')
						else
					--		Print('var["' .. m1 .. '"]=' .. m3 .. '')
							var[m1] = m3
						end
					end
				end
				--			io.close(var.filepath..id.."\\my-config.txt")				
				var["do_stop"] = 0
				var["wrong_way"] = 0			
				var["step_wait"] = var["walk_wait"] / var["walk_step"]			
				var["do_job"] = ""
				add_alias("do_job", function() end)
				var["first_pfm_count"] = num_item(var["first_pfm"], ";") or 10 --���ֹ�����ָ����Ŀ
				var["xiaofu"] = 0
				wait(0.1,function()
					Echo(C.G.."ID->" .. id .. "����·��:" .. var.filepath .. id .. "\\my-config.txt")
					Echo(C.G.."���õ���ɹ�!!!")
				end)
			end
		end		
	else
		for line in io.lines(var.filepath .. "includes\\my-config.txt") do
			local m1, m2 = string.match(line, "^var%[(.*)%]%=(.*)")
			if m1 then
				m1 = string.gsub(m1, '%"', "")
				m2 = string.gsub(m2, '%"', "")
				local m3 = tonumber(m2)
				if m3 == nil then
					var[m1] = m2
					--		Print('var["'..m1..'"]="'..m2..'"')
				else
					--		Print('var["'..m1..'"]='..m3..'')
					var[m1] = m3
				end
			end
		end
		--			io.close(var.filepath.."includes\\my-config.txt")
		var["do_stop"] = 0
		var["wrong_way"] = 0		
		var["step_wait"] = var["walk_wait"] / var["walk_step"]
		var["do_job"] = ""
		var["xiaofu"] = 0	
		add_alias("do_job", function() end)
		var["first_pfm_count"] = num_item(var["first_pfm"], ";") or 10 --���ֹ�����ָ����Ŀ
		Echo("$HIW$����·��:" .. var.filepath .. "includes\\my-config.txt")
		Echo("$HIR$ͨ������·��!!!")
	end
end

-- MUD连接功能
function connect_to_mud(host, port)
	if host == nil or host == "" then
		echo(C.R .. "错误：请指定服务器地址" .. C.D)
		echo(C.Y .. "用法：connect <服务器> <端口>" .. C.D)
		return false
	end

	port = port or 8080  -- 默认端口

	echo(C.G .. "正在连接到 " .. host .. ":" .. port .. "..." .. C.D)

	-- 设置连接参数
	SetVar("mud_host", host)
	SetVar("mud_port", port)

	-- 断开现有连接
	if IsConnect() then
		echo(C.Y .. "断开现有连接..." .. C.D)
		Disconnect()
	end

	-- 连接到新服务器
	Connect(host, port)

	-- 等待连接结果
	add_timer(2, function()
		if IsConnect() then
			echo(C.G .. "✅ 连接成功！" .. C.D)
			echo(C.C .. "服务器：" .. host .. ":" .. port .. C.D)
		else
			echo(C.R .. "❌ 连接失败！" .. C.D)
		end
	end, "check_connection")

	return true
end

function disconnect_from_mud()
	if IsConnect() then
		echo(C.Y .. "正在断开连接..." .. C.D)
		Disconnect()
		echo(C.G .. "已断开连接" .. C.D)
	else
		echo(C.R .. "当前没有连接" .. C.D)
	end
end

function show_connection_status()
	if IsConnect() then
		local host = GetVar("mud_host") or "未知"
		local port = GetVar("mud_port") or "未知"
		echo(C.G .. "✅ 已连接" .. C.D)
		echo(C.C .. "服务器：" .. host .. ":" .. port .. C.D)
	else
		echo(C.R .. "❌ 未连接" .. C.D)
	end
end

function reconnect_to_mud()
	local host = GetVar("mud_host")
	local port = GetVar("mud_port")

	if host and port then
		echo(C.Y .. "正在重新连接..." .. C.D)
		connect_to_mud(host, port)
	else
		echo(C.R .. "没有保存的连接信息" .. C.D)
		echo(C.Y .. "请使用：connect <服务器> <端口>" .. C.D)
	end
end

-- 添加连接相关的别名命令
alias = alias or {}

alias["connect"] = function(params)
	if params[1] and params[2] then
		connect_to_mud(params[1], tonumber(params[2]))
	elseif params[1] then
		connect_to_mud(params[1])
	else
		echo(C.Y .. "用法：connect <服务器> [端口]" .. C.D)
		echo(C.C .. "例如：connect mud.pkuxkx.net 8080" .. C.D)
	end
end

alias["disconnect"] = function(params)
	disconnect_from_mud()
end

alias["status"] = function(params)
	show_connection_status()
end

alias["reconnect"] = function(params)
	reconnect_to_mud()
end

-- 快速连接到常用MUD服务器
alias["pkuxkx"] = function(params)
	connect_to_mud("mud.pkuxkx.net", 8080)
end

alias["nt"] = function(params)
	connect_to_mud("nt.mud.ren", 5555)
end

alias["es"] = function(params)
	connect_to_mud("es2.mud.ren", 5555)
end

-- 显示连接帮助
alias["help_connect"] = function(params)
	echo(C.G .. "=== MUD连接命令帮助 ===" .. C.D)
	echo(C.Y .. "connect <服务器> [端口]" .. C.C .. " - 连接到MUD服务器" .. C.D)
	echo(C.Y .. "disconnect" .. C.C .. " - 断开连接" .. C.D)
	echo(C.Y .. "status" .. C.C .. " - 显示连接状态" .. C.D)
	echo(C.Y .. "reconnect" .. C.C .. " - 重新连接" .. C.D)
	echo(C.G .. "快速连接：" .. C.D)
	echo(C.Y .. "pkuxkx" .. C.C .. " - 连接到北大侠客行" .. C.D)
	echo(C.Y .. "nt" .. C.C .. " - 连接到新唐" .. C.D)
	echo(C.Y .. "es" .. C.C .. " - 连接到恶人谷" .. C.D)
end

-- 启动时显示连接帮助
echo(C.G .. "MUD客户端已加载！" .. C.D)
echo(C.Y .. "输入 help_connect 查看连接命令" .. C.D)

require "zmud"
require "lordstar"