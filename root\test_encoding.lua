-- Test encoding and fix Chinese display issues
print("Testing Chinese character display...")
print("English: Hello World")
print("Chinese: 你好世界")
print("Numbers: 123456")
print("Symbols: !@#$%^&*()")

-- Test if we can detect the encoding
local function detect_encoding()
    local handle = io.popen("chcp", "r")
    if handle then
        local result = handle:read("*a")
        handle:close()
        print("Current code page: " .. (result or "unknown"))
        return result
    end
    return nil
end

detect_encoding()

-- Try to set UTF-8 encoding
print("Attempting to set UTF-8 encoding...")
os.execute("chcp 65001 >nul 2>&1")

print("After encoding change:")
print("Chinese: 你好世界")
print("Test completed.")
