@echo off
chcp 65001 >nul
echo 测试CFAN MUD客户端环境...

set CURRENT_DIR=%cd%
set LUAROCKS_DIR=%CURRENT_DIR%\..\luarocks-3.12.2-win32
set LUA_DIR=%LUAROCKS_DIR%\win32\lua5.1
set ROCKS_DIR=%CURRENT_DIR%\rocks

echo Lua目录: %LUA_DIR%
echo Rocks目录: %ROCKS_DIR%
echo.

if not exist "%LUA_DIR%\bin\lua5.1.exe" (
    echo 错误: 找不到lua5.1.exe
    pause
    exit /b 1
)

set LUA_PATH=./sc/?.lua;./root/?.lua;%ROCKS_DIR%/share/lua/5.1/?.lua;%ROCKS_DIR%/share/lua/5.1/?/init.lua
set LUA_CPATH=%ROCKS_DIR%/lib/lua/5.1/?.dll;%LUA_DIR%/bin/?.dll

cd ..

echo 运行测试脚本...
"%LUA_DIR%\bin\lua5.1.exe" root/test.lua

pause
