#!/bin/sh

[ "$1" ] || {
   echo "usage.....: $0 <release>"
   echo "example...: $0 3.1.3"
   echo
   exit 1
}

v="$1"

git show $v &> /dev/null || {
   echo "There is no release branch $v"
   exit 1
}

git show origin v$v &> /dev/null || {
   echo "There is no pushed tag v$v in origin."
   echo
   echo "Before running this, make sure branch is tagged:"
   echo "   git tag -s v$v $v -m 'Release $v'"
   echo "   git push origin v$v"
   echo
   exit 1
}

git fetch --all
git checkout master
git diff master $v > version.diff
git merge --no-ff $v
patch -R -p1 < version.diff
git add luarocks-dev-1.rockspec
git commit -av --amend
