@echo off
echo 启动CFAN MUD客户端...

REM 设置路径
set LUAROCKS_DIR=%~dp0..\luarocks-3.12.2-win32
set LUA_DIR=%LUAROCKS_DIR%\win32\lua5.1
set ROCKS_DIR=%~dp0rocks
set PATH=%LUA_DIR%\bin;%PATH%

REM 设置Lua路径
set LUA_PATH=./sc/?.lua;./root/?.lua;%ROCKS_DIR%/share/lua/5.1/?.lua;%ROCKS_DIR%/share/lua/5.1/?/init.lua;%LUA_PATH%
set LUA_CPATH=%ROCKS_DIR%/lib/lua/5.1/?.dll;%LUA_DIR%/bin/?.dll;%LUA_CPATH%

echo Lua目录: %LUA_DIR%
echo Rocks目录: %ROCKS_DIR%
echo.

REM 切换到项目根目录
cd /d "%~dp0.."

REM 运行启动脚本
"%LUA_DIR%\bin\lua5.1.exe" root/start.lua

pause
