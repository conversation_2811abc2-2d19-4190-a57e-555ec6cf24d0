local record args
   record Args
      tree: string
      global: boolean
      deps_mode: string
      ["local"]: boolean
      project_tree: string
      server: string
      dev: boolean
      only_server: string
      verbose: string
      lua_version: string
      lua_dir: string
      no_project: boolean
      input: {string}
      nodeps: boolean
      timeout: number
      command: string
      key: string
      value: string
      only_sources: string
      no_manifest: boolean
      force_lock: boolean
      rockspec: string
      namespace: string
      pack_binary_rock: boolean
      only_deps: boolean
      branch: string
      verify: boolean
      check_lua_versions: boolean
      pin: boolean
      no_install: boolean
      sign: boolean
      no_doc: boolean
      keep: boolean
      force: boolean
      force_fast: boolean
      rock: string
      version: string
      scope: string
      lua_incdir: string
      lua_libdir: string
      lua_ver: string
      system_config: string
      user_config: string
      rock_trees: string
      unset: boolean
      json: boolean
      home: boolean
      porcelain: boolean
      list: boolean
      name: string
      all: boolean
      source: string
      arch: string
      location: string
      tag: string
      output: string
      homepage: string
      rockspec_format: string
      summary: string
      detailed: string
      license: string
      lua_versions: string
      lib: string
      no_gitignore: boolean
      no_wrapper_scripts: boolean
      wrapper_dir: string
      reset: boolean
      filter: string
      outdated: boolean
      new_version: string
      dir: string
      new_url: string
      lr_path: string
      lr_cpath: string
      lr_bin: string
      full: boolean
      append: boolean
      no_bin: boolean
      old_versions: boolean
      binary: boolean
      rock_tree: boolean
      rock_namespace: boolean
      rock_dir: boolean
      rock_license: boolean
      issues: boolean
      labels: boolean
      modules: boolean
      deps: boolean
      build_deps: boolean
      test_deps: boolean
      mversion: boolean
      test_type: string
      args: {string}
      prepare: boolean
      src_rock: string
      skip_pack: boolean
      modname: string
      add_server: string
      no_refresh: boolean
      index: boolean
      repository: string
      local_tree: string
      temp_key: string
      api_key: string
      debug: boolean
      rocks: {string}
   end
end

return args
