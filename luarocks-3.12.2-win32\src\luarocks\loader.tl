--- A module which installs a Lua package loader that is LuaRocks-aware.
-- This loader uses dependency information from the LuaRocks tree to load
-- correct versions of modules. It does this by constructing a "context"
-- table in the environment, which records which versions of packages were
-- used to load previous modules, so that the loader chooses versions
-- that are declared to be compatible with the ones loaded earlier.

local loaders = package.loaders or package.searchers
local require, ipairs, table, type, next, tostring, error =
      require, ipairs, table, type, next, tostring, error

local record loader
   context: {string: string}
   luarocks_loader: function(string): LoaderFn | string, any
end

local is_clean = not package.loaded["luarocks.core.cfg"]

-- This loader module depends only on core modules.
local cfg = require("luarocks.core.cfg")
local cfg_ok, _err = cfg.init()
if cfg_ok then
   cfg.init_package_paths()
end

local path = require("luarocks.core.path")
local manif = require("luarocks.core.manif")
local vers = require("luarocks.core.vers")

local type Version = require("luarocks.core.types.version").Version
local type TreeManifest = require("luarocks.core.types.manifest").Tree_manifest
local type Tree = require("luarocks.core.types.tree").Tree
local type FilterFn = function(string, string, string, Tree, integer): string
local type LoaderFn = function()

local record Provider
   name: string
   version: Version
   module_name: string
   tree: TreeManifest
end

--------------------------------------------------------------------------------
-- Backwards compatibility
--------------------------------------------------------------------------------

local record LuaRocksGlobal
   loader: loader
end

global luarocks: LuaRocksGlobal

-- Workaround for wrappers produced by older versions of LuaRocks
local temporary_global = false
local status, luarocks_value = pcall(function(): LuaRocksGlobal
   return luarocks
end)
if status and luarocks_value then
   -- The site_config.lua file generated by old versions uses module(),
   -- so it produces a global `luarocks` table. Since we have the table,
   -- add the `loader` field to make the old wrappers happy.
   luarocks.loader = loader
else
   -- When a new version is installed on top of an old version,
   -- site_config.lua may be replaced, and then it no longer creates
   -- a global.
   -- Detect when being called via -lluarocks.loader; this is
   -- most likely a wrapper.
   local info = debug and debug.getinfo(2, "nS")
   if info and info.what == "C" and not info.name then
      luarocks = { loader = loader }
      temporary_global = true
      -- For the other half of this hack,
      -- see the next use of `temporary_global` below.
   end
end

--------------------------------------------------------------------------------
-- Context management
--------------------------------------------------------------------------------

loader.context = {}

--- Process the dependencies of a package to determine its dependency
-- chain for loading modules.
--
-- @param name     The name of an installed rock.
-- @param version  The version of the rock, in string format
function loader.add_context(name: string, version: string)
   if temporary_global then
      -- The first thing a wrapper does is to call add_context.
      -- From here on, it's safe to clean the global environment.
      luarocks = nil
      temporary_global = false
   end

   local tree_manifests = manif.load_rocks_tree_manifests()
   if not tree_manifests then
      return
   end

   manif.scan_dependencies(name, version, tree_manifests, loader.context)
end

--- Internal sorting function.
--
-- @param a  A provider table.
-- @param b  Another provider table.
--
-- @return  true if the version of a is greater than that of b.
local function sort_versions(a: Provider, b: Provider): boolean
   return a.version > b.version
end

--- Request module to be loaded through other loaders,
-- once the proper name of the module has been determined.
-- For example, in case the module "socket.core" has been requested
-- to the LuaRocks loader and it determined based on context that
-- the version 2.0.2 needs to be loaded and it is not the current
-- version, the module requested for the other loaders will be
-- "socket.core_2_0_2".
--
-- @param module       The module name requested by the user, e.g. "socket.core"
-- @param name         The rock name, such as "luasocket"
-- @param version      The rock version, such as "2.0.2-1"
-- @param module_name  Actual module name, such as "socket.core_2_0_2"
--
-- @return  The loader function returned or an error message.
-- @return  Additional loader data, if returned by the loader.
local function call_other_loaders(module: string, name: string, version: string, module_name: string): LoaderFn | string, any
   for _, a_loader in ipairs(loaders) do
      if a_loader ~= loader.luarocks_loader then
         local results: {any} = { a_loader(module_name) }
         local f = results[1]
         if f is LoaderFn then
            if #results == 2 then
               return f, results[2]
            else
               return f
            end
         end
      end
   end
   return "Failed loading module " .. module .. " in LuaRocks rock " .. name .. " " .. version
end

--- Find entries which provide the wanted module in the tree,
-- and store them in the array of providers for later sorting.
--
-- @param providers    The array of providers being accumulated into
-- @param entries      The packages which provide the module
-- @param tree         TreeManifest where filenames can be found.
-- @param module       The module name being looked up
-- @param filter_name  A filtering function to adjust the filename.
--
-- @return  If the current LuaRocks loader context already resolved this
-- dependency based on other dependencies, return the name of the module
-- for immediate use.
-- @return  Version of the module for immediate use, if matched.
-- @return  File name of the module for immediate use, if matched.
local function add_providers(providers: {Provider}, entries: {string}, tree: TreeManifest, module: string, filter_name: FilterFn): string, string, string
   for i, entry in ipairs(entries) do
      local name, version = entry:match("^([^/]*)/(.*)$")

      local file_name = tree.manifest.repository[name][version][1].modules[module]
      if type(file_name) ~= "string" then
         error("Invalid data in manifest file for module " .. tostring(module) .. " (invalid data for " .. tostring(name) .. " " .. tostring(version) .. ")")
      end

      file_name = filter_name(file_name, name, version, tree.tree, i)

      if loader.context[name] == version then
         return name, version, file_name
      end

      table.insert(providers, {
         name = name,
         version = vers.parse_version(version),
         module_name = file_name,
         tree = tree
      })
   end
end

--- Search for a module in the rocks trees.
--
-- @param module       module name (eg. "socket.core")
-- @param filter_name  a function that takes the module file name
-- (eg "socket/core.so"), the rock name (eg "luasocket"),
-- the version (eg "2.0.2-1"), the path of the rocks tree
-- (eg "/usr/local"), and the numeric index of the matching entry, so the
-- filter function can know if the matching module was the first entry or not.
--
-- @return name of the rock containing the module (eg. "luasocket")
-- @return version of the rock (eg. "2.0.2-1")
-- @return return value of filter_name
local function select_module(module: string, filter_name: FilterFn): string, string, string

   local tree_manifests = manif.load_rocks_tree_manifests()
   if not tree_manifests then
      return nil
   end

   local providers: {Provider} = {}
   local initmodule: string
   for _, tree in ipairs(tree_manifests) do
      local entries = tree.manifest.modules[module]
      if entries then
         local n, v, f = add_providers(providers, entries, tree, module, filter_name)
         if n then
            return n, v, f
         end
      else
         initmodule = initmodule or module .. ".init"
         entries = tree.manifest.modules[initmodule]
         if entries then
            local n, v, f = add_providers(providers, entries, tree, initmodule, filter_name)
            if n then
               return n, v, f
            end
         end
      end
   end

   if next(providers) then
      table.sort(providers, sort_versions)
      local first = providers[1]
      return first.name, first.version.string, first.module_name
   end
end

--- Filter operation for adjusting the versioned names when multiple packages provide
-- the same file.
--
-- @param file_name  The original filename
-- @param name       The rock name
-- @param version    The rock version
-- @param _tree      (unused)
-- @param i          The priority index, to determine whether to version the name.
--
-- @return  A filename, which may be plain or versioned.
-- (eg. "socket.core", or "socket.core_2_0_2" if file is stored versioned).
local function filter_module_name(file_name: string, name: string, version: string, _tree: Tree, i: integer): string
   if i > 1 then
      file_name = path.versioned_name(file_name, "", name, version)
   end
   return path.path_to_module(file_name)
end

--- Search for a module.
--
-- @param module  name of the module (eg. "socket.core")
--
-- @return  name of the rock containing the module (eg. "luasocket")
-- @return  version of the rock (eg. "2.0.2-1")
-- @return  name of the module (eg. "socket.core", or "socket.core_2_0_2" if file is stored versioned).
-- @return  tree of the module (string or table in `tree_manifests` format)
local function pick_module(module: string): string, string, string, string | TreeManifest
   return select_module(module, filter_module_name)
end

--- Return the pathname of the file that would be loaded for a module.
--
-- @param module  module name (eg. "socket.core")
-- @param where   places to look for the module. If `where` contains
-- "l", it will search using the LuaRocks loader; if it contains "p",
-- it will look in the filesystem using package.path and package.cpath.
-- You can use both at the same time.
--
-- @return  If found, filename of the module (eg. "/usr/local/lib/lua/5.1/socket/core.so"),
-- @return  If found via the loader, the rock name; otherwise, "path" or "cpath"
-- @return  If found via the loader, the rock version; otherwise, nil
-- @return  If found via the loader, "l"; if found via package.path or package.cpath, "p".
function loader.which(module: string, where?: string): string, string, string, string
   where = where or "l"
   if where:match("l") then
      local rock_name, rock_version, file_name = select_module(module, path.which_i)
      if rock_name then
         local fd = io.open(file_name)
         if fd then
            fd:close()
            return file_name, rock_name, rock_version, "l"
         end
      end
   end
   if where:match("p") then
      local modpath = module:gsub("%.", "/")
      for _, v in ipairs({package.path, package.cpath}) do
         for p in v:gmatch("([^;]+)") do
            local file_name = p:gsub("%?", modpath)  -- luacheck: ignore 421
            local fd = io.open(file_name)
            if fd then
               fd:close()
               return file_name, v, nil, "p"
            end
         end
      end
   end
end

--- Package loader for LuaRocks support.
-- See <a href="http://www.lua.org/manual/5.4/manual.html#pdf-require">require()</a>
-- in the Lua reference manual for details on the require() mechanism.
-- The LuaRocks loader works by searching in installed rocks that match the
-- current LuaRocks context. If module is not part of the
-- context, or if a context has not yet been set, the module
-- in the package with the highest version is used.
--
-- @param module  The module name, like in plain require().
--
-- @return  A function which can load the module found,
-- or a string with an error message.
function loader.luarocks_loader(module: string): LoaderFn | string, any
   local name, version, module_name = pick_module(module)
   if not name then
      return "No LuaRocks module found for " .. module
   else
      loader.add_context(name, version)
      return call_other_loaders(module, name, version, module_name)
   end
end

table.insert(loaders, 1, loader.luarocks_loader)

if is_clean then
   for modname, _ in pairs(package.loaded) do
      if modname:match("^luarocks%.") then
         package.loaded[modname] = nil
      end
   end
end

return loader
