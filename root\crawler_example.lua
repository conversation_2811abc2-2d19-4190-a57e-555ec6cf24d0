-- 网络爬虫使用示例

local crawler = require("web_crawler")

print("=== 网络爬虫示例程序 ===")
print()

-- 示例1: 单个URL爬取
print("示例1: 爬取单个网页")
local url = "http://httpbin.org/html"
local response, err = crawler.crawl(url, {
    save_raw = true,
    save_body = true
})

if response then
    print("✅ 爬取成功!")
    print("状态码:", response.status_code)
    print("响应体长度:", #response.body)
else
    print("❌ 爬取失败:", err)
end

print()
print("=" .. string.rep("=", 50))
print()

-- 示例2: 批量URL爬取
print("示例2: 批量爬取多个网页")
local urls = {
    "http://httpbin.org/json",
    "http://httpbin.org/xml",
    "http://httpbin.org/html"
}

local results = crawler.crawl_multiple(urls, {
    save_body = true
})

print("批量爬取结果:")
for i, result in ipairs(results) do
    local status = result.success and "✅ 成功" or "❌ 失败"
    print(string.format("%d. %s - %s", i, result.url, status))
    if not result.success then
        print("   错误:", result.error)
    end
end

print()
print("=" .. string.rep("=", 50))
print()

-- 示例3: 显示爬取统计
print("示例3: 爬取统计")
local success_count = 0
local total_size = 0

for _, result in ipairs(results) do
    if result.success then
        success_count = success_count + 1
        if result.response and result.response.body then
            total_size = total_size + #result.response.body
        end
    end
end

print("总URL数:", #urls)
print("成功数:", success_count)
print("成功率:", string.format("%.1f%%", (success_count / #urls) * 100))
print("总数据量:", total_size, "字节")

print()
print("程序执行完成!")
print("查看 crawled_data 目录获取下载的文件")
print("查看 crawler.log 获取详细日志")
