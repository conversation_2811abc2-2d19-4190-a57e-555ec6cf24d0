================================================================================
TEST: luarocks test: handle if test.command is not a string

Regression test for #1055.

FILE: example-1.0-1.rockspec
--------------------------------------------------------------------------------
rockspec_format = "3.0"
source = {
   url = "",
}
package = "example"
version = "1.0-1"
test = {
   type = "command",
   command = {"./unit.lua"},
}
--------------------------------------------------------------------------------

RUN: luarocks test
EXIT: 1
STDERR:
--------------------------------------------------------------------------------
'command' expects a string
--------------------------------------------------------------------------------



================================================================================
TEST: luarocks test: handle if test.script is not a string

FILE: example-1.0-1.rockspec
--------------------------------------------------------------------------------
rockspec_format = "3.0"
source = {
   url = "",
}
package = "example"
version = "1.0-1"
test = {
   type = "command",
   script = {"./unit.lua"},
}
--------------------------------------------------------------------------------

RUN: luarocks test
EXIT: 1
STDERR:
--------------------------------------------------------------------------------
'script' expects a string
--------------------------------------------------------------------------------
