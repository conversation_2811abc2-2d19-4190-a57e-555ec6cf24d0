#!/usr/bin/env lua5.1
-- MUD客户端启动脚本

-- 设置工作目录为项目根目录
local lfs = require("lfs")
local current_dir = lfs.currentdir()

-- 确保我们在正确的目录
if not string.find(current_dir, "cfanmud") then
    print("警告: 当前目录可能不正确")
    print("当前目录: " .. current_dir)
end

-- 添加项目路径到package.path
local rocks_path = "./root/rocks"
package.path = package.path .. ";./sc/?.lua;./root/?.lua;" .. rocks_path .. "/share/lua/5.1/?.lua;" .. rocks_path .. "/share/lua/5.1/?/init.lua"
package.cpath = package.cpath .. ";./luarocks-3.12.2-win32/win32/lua5.1/bin/?.dll;" .. rocks_path .. "/lib/lua/5.1/?.dll"

print("=== CFAN MUD 客户端 ===")
print("Lua版本: " .. _VERSION)
print("工作目录: " .. current_dir)

-- 检查必要的文件
local function check_file(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

print("\n检查必要文件...")
local required_files = {
    "sc/main.lua",
    "root/config.lua",
    "root/mud_client.lua"
}

local all_files_exist = true
for _, file_path in ipairs(required_files) do
    if check_file(file_path) then
        print("✓ " .. file_path)
    else
        print("✗ " .. file_path .. " (文件不存在)")
        all_files_exist = false
    end
end

if not all_files_exist then
    print("\n错误: 缺少必要文件，请检查项目结构")
    return
end

-- 检查luasocket
print("\n检查依赖...")
local socket_ok, socket = pcall(require, "socket")
if socket_ok then
    print("✓ luasocket 可用")
else
    print("✗ luasocket 不可用: " .. tostring(socket))
    print("请确保luasocket已正确安装")
end

local lfs_ok, lfs_module = pcall(require, "lfs")
if lfs_ok then
    print("✓ lfs 可用")
else
    print("✗ lfs 不可用: " .. tostring(lfs_module))
end

-- 启动客户端
if socket_ok then
    print("\n启动MUD客户端...")
    local client = require("mud_client")
    
    -- 可以在这里添加自动连接逻辑
    -- client.connect("pkuxkx")  -- 自动连接到指定服务器
    
    client.run()
else
    print("\n无法启动客户端: luasocket不可用")
end
