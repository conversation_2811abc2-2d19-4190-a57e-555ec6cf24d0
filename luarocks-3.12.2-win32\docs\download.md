# Download

# Downloading

* [Releases](https://luarocks.github.io/luarocks/releases/)
* [Changelog](../CHANGELOG.md)

# Installing

* [Installation instructions for Unix](installation_instructions_for_unix.md) (Linux, BSDs, etc.)
* [Installation instructions for macOS](installation_instructions_for_macos.md)
* [Installation instructions for Windows](installation_instructions_for_windows.md)

LuaRocks is a pure Lua application with no library dependencies. Its current
release depends on some helper tools, but you shouldn't worry about them, as
they are shipped by default on most Unix systems. For Windows, the all-in-one
package already includes them and a binary of Lua, as a convenience.

Once Lua<PERSON><PERSON> is installed, make sure to read the
[Documentation](index.md) for more information!

# The bleeding edge

Development is done using Git. To get the latest development sources, run:

```
git clone git://github.com/luarocks/luarocks.git
```

There is also a web interface available at:

* [https://github.com/luarocks/luarocks](https://github.com/luarocks/luarocks)

