-- Test MUD Client

print("=== MUD Client Test ===")

-- Load the MUD client
local mud = require("mud_client")

print("MUD Client loaded successfully!")
print()

-- Test connection (using a common MUD testing server)
print("Testing connection to a MUD server...")
print("Attempting to connect to localhost:4000 (common MUD port)")

local connected = mud.connect("localhost", 4000)

if connected then
    print("Connection successful!")
    
    -- Test sending a command
    print("Sending test command: 'look'")
    mud.send_command("look")
    
    -- Wait a bit and try to receive data
    print("Waiting for server response...")
    
    -- Simple receive test
    local data = mud.receive_data()
    if data then
        local processed = mud.process_output(data)
        print("Received from server:")
        print(processed)
    else
        print("No data received (this is normal if no MUD server is running)")
    end
    
    -- Disconnect
    mud.disconnect()
else
    print("Connection failed - this is expected if no MUD server is running locally")
end

print()
print("=== MUD Client Features ===")
print("1. Connect to MUD servers")
print("2. Send commands to server")
print("3. Receive and process server responses")
print("4. Remove ANSI color codes")
print("5. Log all activity")
print()
print("To use the MUD client:")
print("  local mud = require('mud_client')")
print("  mud.connect('hostname', port)")
print("  mud.send_command('your command')")
print("  local data = mud.receive_data()")
print("  mud.disconnect()")
print()
print("Test completed!")
