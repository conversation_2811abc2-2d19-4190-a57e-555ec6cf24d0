#!/usr/bin/env bash

[ "$1" ] || {
   echo "usage.....: $0 <version>"
   echo "example...: $0 3.1.1"
   echo
   echo "Before running this, make sure the packages were built:"
   echo "   makedist 3.1.1 /opt/lua54/ branch binary sign"
   echo "And the tag was merged:"
   echo "   mergerelease 3.1.1"
   echo
   exit 1
}

#######################################
# preliminary checks
#######################################

v="$1"

git checkout v$v || {
   echo "Could not checkout release tag."
}

packages=(
   luarocks-$v-windows-32.zip
   luarocks-$v-windows-32.zip.asc
   luarocks-$v-windows-64.zip
   luarocks-$v-windows-64.zip.asc
   luarocks-$v-linux-x86_64.zip
   luarocks-$v-linux-x86_64.zip.asc
   luarocks-$v-win32.zip
   luarocks-$v-win32.zip.asc
   luarocks-$v.tar.gz
   luarocks-$v.tar.gz.asc
)

for f in "${packages[@]}" luarocks-$v-1.rockspec
do
   [ -e "$f" ] || {
      echo "Missing file $f"
      exit 1
   }
done

source_digest="$(
   sha256sum "luarocks-${v}.tar.gz" | cut -d' ' -f1
)"

#######################################
# utility
#######################################

function confirm() {
   branch="$1"

   echo "****************************************"
   git diff $branch
   echo "****************************************"
   git status
   echo "****************************************"

   echo "Everything looks all right? (y/n)"
   echo "(Answering y will commit and push)"
   read
   if ! [ "$REPLY" == "y" ]
   then
      git reset
      git checkout .
      git checkout master
      exit 1
   fi
}

#######################################
# luarocks.org
#######################################

luarocks upload luarocks-$v-1.rockspec

#######################################
# gh-pages
#######################################

git checkout gh-pages
git fetch origin gh-pages
git reset --hard origin/gh-pages

cp "${packages[@]}" releases
cd releases
git add "${packages[@]}"
gawk '
/add new release here/ {
   print "<!-- add new release here -->"
   print ""
   print "<tr><td><a href=\"luarocks-'$v'.tar.gz\">luarocks-'$v'.tar.gz</a></td><td><a href=\"luarocks-'$v'.tar.gz.asc\">PGP signature</a></td></tr>"
   print "<tr><td><a href=\"luarocks-'$v'-windows-32.zip\">luarocks-'$v'-windows-32.zip</a> (luarocks.exe stand-alone Windows 32-bit binary)</td><td><a href=\"luarocks-'$v'-windows-32.zip.asc\">PGP signature</a></td></tr>"
   print "<tr><td><a href=\"luarocks-'$v'-windows-64.zip\">luarocks-'$v'-windows-64.zip</a> (luarocks.exe stand-alone Windows 64-bit binary)</td><td><a href=\"luarocks-'$v'-windows-64.zip.asc\">PGP signature</a></td></tr>"
   print "<tr><td><a href=\"luarocks-'$v'-linux-x86_64.zip\">luarocks-'$v'-linux-x86_64.zip</a> (luarocks stand-alone Linux x86_64 binary)</td><td><a href=\"luarocks-'$v'-linux-x86_64.zip.asc\">PGP signature</a></td></tr>"
   print "<tr><td><a href=\"luarocks-'$v'-win32.zip\">luarocks-'$v'-win32.zip</a> (legacy Windows package, includes Lua 5.1)</td><td><a href=\"luarocks-'$v'-win32.zip.asc\">PGP signature</a></td></tr>"
   done = 1
}
// {
   if (done == 1) {
      done = 0
   } else {
      print
   }
}
' index.html > index.html.1
mv index.html.1 index.html
git add index.html

gawk '
/^\[$/ {
   go = 1
}
// {
   print
   if (go == 1) {
      go = 0

      print "{"
      print "\"'$v'\": {"
      print "\"date\": \"'$(date +'%Y-%m-%d')'\","
      print "\"files\": [\"luarocks-'$v'.tar.gz\", \"luarocks-'$v'.tar.gz.asc\", \"luarocks-'$v'-win32.zip\", \"luarocks-'$v'-win32.zip.asc\", \"luarocks-'$v'-windows-32.zip\", \"luarocks-'$v'-windows-32.zip.asc\", \"luarocks-'$v'-windows-64.zip\", \"luarocks-'$v'-windows-64.zip.asc\", \"luarocks-'$v'-linux-x86_64.zip\", \"luarocks-'$v'-linux-x86_64.zip.asc\"],"
      print "\"about\": []"
      print "\"source_digest\": \"'$source_digest'\""
      print "}},"
   }
}
' releases.json > releases.json.1
mv releases.json.1 releases.json
git add releases.json

confirm gh-pages

git commit -av -m "Release $v"
git push

#######################################
# luarocks.org
#######################################

git checkout v$v

luarocks upload luarocks-$v-1.rockspec

git checkout master

#######################################
# luarocks-site
#######################################

if [ -e ../luarocks-site ]
then
   cd ../luarocks-site
   git pull
else
   cd ..
   git clone ssh://**************/luarocks/luarocks-site
   cd luarocks-site
fi

sed -i 's,luarocks-[0-9]*\.[0-9]*\.[0-9]*,luarocks-'$v',' static/md/home.md
git add static/md/home.md

confirm master

git commit static/md/home.md -m "update front page for LuaRocks $v"
git push

