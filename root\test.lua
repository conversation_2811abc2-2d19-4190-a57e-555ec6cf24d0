-- 测试脚本，验证环境配置

print("=== 环境测试 ===")

-- 测试Lua版本
print("Lua版本: " .. _VERSION)

-- 测试package路径
print("\nPackage路径:")
for path in string.gmatch(package.path, "[^;]+") do
    print("  " .. path)
end

print("\nPackage C路径:")
for path in string.gmatch(package.cpath, "[^;]+") do
    print("  " .. path)
end

-- 测试luasocket
print("\n测试luasocket...")
local socket_ok, socket = pcall(require, "socket")
if socket_ok then
    print("✓ luasocket加载成功")
    print("  版本: " .. (socket._VERSION or "未知"))
else
    print("✗ luasocket加载失败: " .. tostring(socket))
end

-- 测试lfs
print("\n测试lfs...")
local lfs_ok, lfs = pcall(require, "lfs")
if lfs_ok then
    print("✓ lfs加载成功")
    print("  当前目录: " .. lfs.currentdir())
else
    print("✗ lfs加载失败: " .. tostring(lfs))
end

-- 测试配置文件
print("\n测试配置文件...")
local config_ok, config = pcall(require, "config")
if config_ok then
    print("✓ 配置文件加载成功")
    print("  默认服务器: " .. config.default_server.host .. ":" .. config.default_server.port)
else
    print("✗ 配置文件加载失败: " .. tostring(config))
end

-- 测试适配器
print("\n测试适配器...")
local adapter_ok, adapter = pcall(require, "sc_adapter")
if adapter_ok then
    print("✓ 适配器加载成功")
else
    print("✗ 适配器加载失败: " .. tostring(adapter))
end

-- 测试主脚本
print("\n测试主脚本...")
local main_ok, err = pcall(dofile, "sc/main.lua")
if main_ok then
    print("✓ 主脚本加载成功")
else
    print("✗ 主脚本加载失败: " .. tostring(err))
    print("  尝试其他路径...")
    local main_ok2, err2 = pcall(dofile, "../sc/main.lua")
    if main_ok2 then
        print("✓ 主脚本加载成功 (使用../sc/main.lua)")
    else
        print("✗ 主脚本仍然加载失败: " .. tostring(err2))
    end
end

print("\n=== 测试完成 ===")
