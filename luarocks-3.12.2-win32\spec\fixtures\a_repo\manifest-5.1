commands = {}
modules = {}
repository = {
   a_build_dep = {
      ["1.0-1"] = {
         {
            arch = "src"
         },
         {
            arch = "rockspec"
         }
      }
   },
   a_rock = {
      ["1.0-1"] = {
         {
            arch = "src"
         },
         {
            arch = "rockspec"
         }
      },
      ["2.0-1"] = {
         {
            arch = "src"
         }
      }
   },
   busted_project = {
      ["0.1-1"] = {
         {
            arch = "src"
         },
         {
            arch = "rockspec"
         }
      }
   },
   has_another_namespaced_dep = {
      ["1.0-1"] = {
         {
            arch = "rockspec"
         },
         {
            arch = "src"
         }
      }
   },
   has_build_dep = {
      ["1.0-1"] = {
         {
            arch = "rockspec"
         },
         {
            arch = "src"
         },
         {
            arch = "all"
         }
      }
   },
   has_namespaced_dep = {
      ["1.0-1"] = {
         {
            arch = "rockspec"
         },
         {
            arch = "src"
         }
      }
   },
   non_lua_file = {
      ["1.0-1"] = {
         {
            arch = "rockspec"
         },
         {
            arch = "src"
         }
      },
      ["1.0-2"] = {
         {
            arch = "rockspec"
         },
         {
            arch = "src"
         }
      }
   }
}
