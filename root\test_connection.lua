-- 测试网络连接的脚本
local socket

-- 尝试加载luasocket，如果失败则使用我们的简单实现
local success, luasocket = pcall(require, "socket")
if success then
    socket = luasocket
    print("使用真实的luasocket")
else
    print("luasocket不可用，使用模拟socket")
    socket = require("simple_socket")
end

print("开始测试网络连接...")

-- 测试连接到百度
local function test_connection(host, port)
    print("尝试连接到 " .. host .. ":" .. port)
    
    local tcp = socket.tcp()
    if tcp.settimeout then
        tcp:settimeout(5)  -- 5秒超时
    end
    
    local result, err = tcp:connect(host, port)
    
    if result then
        print("连接成功!")
        if tcp.close then
            tcp:close()
        end
        return true
    else
        print("连接失败: " .. (err or "未知错误"))
        return false
    end
end

-- 测试多个网站
local test_sites = {
    {"www.baidu.com", 80},
    {"www.google.com", 80},
    {"httpbin.org", 80}
}

print("测试网络连接...")
for i, site in ipairs(test_sites) do
    local host, port = site[1], site[2]
    print("\n测试 " .. i .. ": " .. host)
    test_connection(host, port)
end

print("\n网络连接测试完成!")
