-- Network connection test script
local socket

-- Try to load luasocket, fallback to simple implementation if failed
local success, luasocket = pcall(require, "socket")
if success then
    socket = luasocket
    print("Using real luasocket")
else
    print("luasocket not available, using mock socket")
    socket = require("simple_socket")
end

print("Starting network connection test...")

-- Test connection function
local function test_connection(host, port)
    print("Attempting to connect to " .. host .. ":" .. port)

    local tcp = socket.tcp()
    if tcp.settimeout then
        tcp:settimeout(5)  -- 5 second timeout
    end

    local result, err = tcp:connect(host, port)

    if result then
        print("Connection successful!")
        if tcp.close then
            tcp:close()
        end
        return true
    else
        print("Connection failed: " .. (err or "unknown error"))
        return false
    end
end

-- Test multiple websites
local test_sites = {
    {"www.baidu.com", 80},
    {"www.google.com", 80},
    {"httpbin.org", 80}
}

print("Testing network connections...")
for i, site in ipairs(test_sites) do
    local host, port = site[1], site[2]
    print("\nTest " .. i .. ": " .. host)
    test_connection(host, port)
end

print("\nNetwork connection test completed!")
