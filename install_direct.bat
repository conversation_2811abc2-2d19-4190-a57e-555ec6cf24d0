@echo off
echo 直接安装luasocket...
echo.

cd root

echo 创建rocks目录...
if not exist rocks mkdir rocks
if not exist rocks\lib mkdir rocks\lib
if not exist rocks\share mkdir rocks\share

echo.
echo 尝试安装luasocket...
echo 注意：这可能需要几分钟时间

..\luarocks-3.12.2-win32\win32\lua5.1\bin\lua5.1.exe ..\luarocks-3.12.2-win32\src\bin\luarocks install luasocket --tree=rocks

echo.
echo 安装lfs...
..\luarocks-3.12.2-win32\win32\lua5.1\bin\lua5.1.exe ..\luarocks-3.12.2-win32\src\bin\luarocks install luafilesystem --tree=rocks

echo.
echo 检查安装结果...
..\luarocks-3.12.2-win32\win32\lua5.1\bin\lua5.1.exe ..\luarocks-3.12.2-win32\src\bin\luarocks list --tree=rocks

cd ..
echo.
echo 安装完成！
pause
