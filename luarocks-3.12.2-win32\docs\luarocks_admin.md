# luarocks-admin

**luarocks-admin** is the repository management tool for LuaRocks, the Lua package manager.

# Usage

```
luarocks-admin <command> [<arguments>]...
```

# Supported commands

* [help](luarocks_admin_help.md) - Help on commands.
* [make-manifest](luarocks_admin_make_manifest.md) - Compile a manifest file for a repository.
* [add](luarocks_admin_add.md) - Add a rock or rockspec to a rocks server.
* [remove](luarocks_admin_remove.md) - Remove a rock or rockspec from a rocks server.
* [refresh-cache](luarocks_admin_refresh_cache.md) - Refresh local cache of a remote rocks server.


