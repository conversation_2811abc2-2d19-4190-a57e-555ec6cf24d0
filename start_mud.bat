@echo off
chcp 65001 >nul
echo 启动CFAN MUD客户端...
echo.

if not exist "root" (
    echo 错误: 找不到root目录
    pause
    exit /b 1
)

if not exist "luarocks-3.12.2-win32" (
    echo 错误: 找不到luarocks-3.12.2-win32目录
    pause
    exit /b 1
)

if not exist "root\rocks" (
    echo 首次运行，需要安装依赖...
    echo 正在运行安装脚本...
    cd root
    call install_luasocket.bat
    cd ..
    echo.
)

echo 启动MUD客户端...
cd root
call run.bat
cd ..
