@echo off
echo 启动CFAN MUD客户端...
echo.

REM 检查root目录是否存在
if not exist "root" (
    echo 错误: 找不到root目录
    pause
    exit /b 1
)

REM 检查luarocks目录是否存在
if not exist "luarocks-3.12.2-win32" (
    echo 错误: 找不到luarocks-3.12.2-win32目录
    pause
    exit /b 1
)

REM 检查是否已安装依赖
if not exist "root\rocks" (
    echo 首次运行，需要安装依赖...
    echo 正在运行安装脚本...
    call root\install_luasocket.bat
    echo.
)

REM 运行客户端
echo 启动MUD客户端...
call root\run.bat
