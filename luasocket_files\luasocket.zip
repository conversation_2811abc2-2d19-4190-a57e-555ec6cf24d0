<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="description" lang="en" content="Unreal Software - game dev and stuff">
	<meta name="author" content="<PERSON>">
	<meta name="keywords" content="Unreal Software, <PERSON>, Stranded, CS2D, Carnage Contest, Survival, Independent, Game, Development"><link href="/fontawesome/css/fontawesome.min.css" rel="stylesheet">
	<link href="/fontawesome/css/brands.min.css" rel="stylesheet">
	<link href="/fontawesome/css/solid.min.css" rel="stylesheet">
	<link href="/css/highlight-vs2015.min.css" rel="stylesheet">
	
	<link href="/favicon.ico" rel="icon">
	<link href="/css/style.css?v=15" rel="stylesheet"><script src="/js/script.js?v=10"></script>
	<script src="/js/highlight.min.js"></script>
	<title>Unreal Software - game dev and stuff</title>
</head>

<body>
<main><h1 class="inline"><a href="get.php">Download</a></h1> <img class="vm" src="img/i_next.png" alt="&gt;"> 
	<img class="vm" src="img/i_file.png" alt="file"> lua_51_luasocket_30_x86_windowslinuxmacos.zip<div class="cols"><div><div class="alt"><p>
				The file <b>lua_51_luasocket_30_x86_windowslinuxmacos.zip</b> has been requested.
				Please click <a rel="nofollow" href="get.php?get=u28e0_551c4506.zip&amp;p=2&amp;cid=13545" download="lua_51_luasocket_30_x86_windowslinuxmacos.zip">Download</a> to start downloading this file!
			</p><div class="file"><h2><a class="il" rel="nofollow" href="get.php?get=u28e0_551c4506.zip&amp;p=2&amp;cid=13545" download="lua_51_luasocket_30_x86_windowslinuxmacos.zip">
				<img class="vm" src="img/i_download.png" alt="Download"> Download
				</a></h2>
			<p>Size: <b>286 kb</b><br>Downloads: <b>1,018</b><br></p></div><p class="detail">Link for sharing: <a rel="nofollow" href="https://www.unrealsoftware.de/get.php?get=u28e0_551c4506.zip&amp;p=2">https://www.unrealsoftware.de/get.php?get=u28e0_551c4506.zip&amp;p=2</a></p><p>Problems? Please deactivate the download manager and browser add-ons or use a different browser.</p><div class="err"><i class="fa-solid fa-skull-crossbones"></i> WARNING: File has been uploaded by a user. It might be dangerous! Use at your own risk!</div><a class="l-file ma-top" href="files_show.php?file=16117">View this file in the file archive</a></div></div><div class="alt center" style="display:flex; justify-content:center; align-items:center;"><div>
			<h3>Thank you! <img src="img/smiles/heart.gif" alt="heart"></h3>
			<p>If you like our free content,<p>
			<a href="get.php?get=u28e0_551c4506.zip&amp;p=2&amp;ad-consent">ENABLE ADS TO SUPPORT US!</a>
			<p class="detail">Advertising is only displayed here. Data is sent to Google and third-party providers. See our <a href="privacy.php">privacy policy</a> for details.</p>
		</div></div></div><hr><div class="alt"><h3>What to do with this file?</h3><p>This file has been uploaded by a <a class="js-tt" href="profile.php?userid=10464">user</a>:</p><br><p><img class="vm" src="img/icons/us.png" alt="&gt;"> <a href="files.php?g=5&amp;lan=2">Stuff</a> <img class="vm" src="img/i_next.png" alt="&gt;"> <a href="files_cat.php?cat=13&amp;lan=2">Misc. Files</a> <img class="vm" src="img/i_next.png" alt="&gt;"> <a class="js-tt" href="files_show.php?file=16117">[Lua 5.1] LuaSocket 3.0 x86 Windows/Linux/MacOS</a></p><h3>Guide</h3>
					There is no installation guide for this file type.
					Please note <a href="files_show.php?file=16117">the file description</a> and readme files (if there are any)!
					Ask the <a class="js_tt" href="profile.php?userid=10464">author of this file</a> if you have any problems/questions while using or installing ths file!</div></main><footer>
		<div>
			<div class="cols">
				<div>
					<a href="settings.php"><i class="fa-solid fa-gear fa-fw"></i> Settings</a>
					<a href="stats.php"><i class="fa-solid fa-chart-simple fa-fw"></i> Stats</a>
				</div>
				<div>
					<a href="donate.php"><i class="fa-solid fa-piggy-bank fa-fw"></i> Donate</a>
					<a href="dev.php"><i class="fa-solid fa-code fa-fw"></i> Dev</a>
				</div>
				<div>
					<a href="privacy.php"><i class="fa-solid fa-shield-halved fa-fw"></i> Privacy Policy</a>
					<a href="contact.php"><i class="fa-solid fa-address-book fa-fw"></i> Contact</a>
				</div>
			</div>
		</div>
	</footer>

	<header>
		<a id="top-btn" href="#top"><i class="fa-solid fa-arrow-up"></i></a>

		<nav>
			<div class="nav-main">
				<div class="drpdwn">
					<button type="button" onclick="desktopLink('/index.php')"><img id="portal-icon" class="vm" src="/img/ulogosmall.gif" alt="US"></button>
					<div>
						<a href="/index.php"><i class="fa-solid fa-house fa-fw"></i> Portal</a>
						<a href="/news.php"><i class="fa-solid fa-newspaper fa-fw"></i> News</a>
						<a href="/about.php"><i class="fa-solid fa-circle-info fa-fw"></i> About</a>
						<a href="/contact.php"><i class="fa-solid fa-address-card fa-fw"></i> Contact</a>
					</div>
				</div>

				<div class="drpdwn">
					<button>Games</button>
					<div>
						<a href="/game_cs2d.php"><img class="vm" src="/img/icons/cs2d.png" alt="icon"> CS2D</a>
						<a href="/game_stranded.php"><img class="vm" src="/img/icons/stranded.png" alt="icon"> Stranded I</a>
						<a href="/game_stranded2.php"><img class="vm" src="/img/icons/stranded2.png" alt="icon"> Stranded II</a>
						<a href="/game_stranded3.php"><img class="vm" src="/img/icons/stranded3.png" alt="icon"> Stranded III</a>
						<a href="/game_cc.php"><img class="vm" src="/img/icons/cc.png" alt="icon"> Carnage Contest</a>
						<a href="/game_minigolf.php"><img class="vm" src="/img/icons/minigolf.png" alt="icon"> Minigolf Madness</a>
						
					</div>
				</div>

				<div class="drpdwn">
					<button type="button" onclick="desktopLink('/forum.php')">Community</button>
					<div>
						<a href="/forum.php"><i class="fa-solid fa-comments fa-fw"></i> Forum</a>
						<a href="/files.php"><i class="fa-solid fa-folder-open fa-fw"></i> Files</a>
						<a href="/users.php"><i class="fa-solid fa-users fa-fw"></i> Users</a>
						<a href="/discord.php"><i class="fa-brands fa-discord fa-fw"></i> Discord</a>
						<a href="/search.php"><i class="fa-solid fa-circle-question fa-fw"></i> Search / FAQ</a>
						<a href="/rules.php"><i class="fa-solid fa-section fa-fw"></i> Rules</a>
						<a href="/usgn.php"><i class="fa-solid fa-circle-nodes fa-fw"></i> U.S.G.N.</a>
					</div>
				</div>
			</div>

			<div class="nav-side"><a class="js-tt" data-tt-plain="1" href="?set_lan=de&amp;sah=95643f89&amp;get=u28e0_551c4506.zip&amp;p=2&amp;id=0&amp;popup=0" title="Switch language">
					<img class="vm nav-outline" src="/img/en1.gif" width="24" height="15" alt="en">
				</a><a class="js-tt" href="login.php" title="Login"><i class="fa-solid fa-user"></i></a>	</div>
		</nav>
	</header>
<script> 
	var set_tt=0;
</script>
</body></html>