-- Standalone MUD Client in Pure Lua
-- Can run with lua5.1.exe directly

-- Try to load luasocket, fallback to mock if not available
local socket
local success, luasocket = pcall(require, "socket")
if success then
    socket = luasocket
    print("Using real luasocket for network connection")
else
    -- Simple mock socket for testing
    socket = {
        tcp = function()
            return {
                connect = function(self, host, port)
                    print("Mock: Connecting to " .. host .. ":" .. port)
                    return true
                end,
                send = function(self, data)
                    print("Mock: Sending - " .. data:gsub("\n", ""))
                    return #data
                end,
                receive = function(self, pattern)
                    return "Mock server response\n"
                end,
                close = function(self)
                    print("Mock: Connection closed")
                end,
                settimeout = function(self, timeout)
                    -- Do nothing
                end
            }
        end
    }
    print("Warning: Using mock socket - no real network connection")
end

-- MUD Client class
local MudClient = {}
MudClient.__index = MudClient

function MudClient:new()
    local client = {
        connected = false,
        socket = nil,
        host = nil,
        port = nil,
        running = false
    }
    setmetatable(client, MudClient)
    return client
end

function MudClient:connect(host, port)
    if not host then
        print("Error: Please specify server address")
        print("Usage: connect <server> <port>")
        return false
    end
    
    port = port or 8080
    
    print("Connecting to " .. host .. ":" .. port .. "...")
    
    self.host = host
    self.port = port
    
    -- Create TCP socket
    self.socket = socket.tcp()
    self.socket:settimeout(5)  -- 5 second timeout
    
    -- Attempt connection
    local result, err = self.socket:connect(host, port)
    
    if result then
        self.connected = true
        print("✅ Connected successfully!")
        print("Server: " .. host .. ":" .. port)
        return true
    else
        print("❌ Connection failed: " .. (err or "unknown error"))
        return false
    end
end

function MudClient:disconnect()
    if self.connected and self.socket then
        self.socket:close()
        self.connected = false
        print("Disconnected from server")
    else
        print("Not connected")
    end
end

function MudClient:send_command(command)
    if not self.connected then
        print("Error: Not connected to server")
        return false
    end
    
    -- Add newline if not present
    if not command:match("\n$") then
        command = command .. "\n"
    end
    
    local bytes_sent, err = self.socket:send(command)
    if bytes_sent then
        print("Sent: " .. command:gsub("\n", ""))
        return true
    else
        print("Error sending command: " .. (err or "unknown error"))
        return false
    end
end

function MudClient:receive_data()
    if not self.connected then
        return nil
    end
    
    self.socket:settimeout(0.1)  -- Non-blocking
    local data, err = self.socket:receive("*l")  -- Read line
    
    if data then
        return data
    elseif err == "timeout" then
        return nil  -- Normal for non-blocking
    else
        print("Connection lost: " .. (err or "unknown error"))
        self.connected = false
        return nil
    end
end

function MudClient:process_output(text)
    if not text then return "" end
    
    -- Remove ANSI escape sequences
    text = text:gsub("\27%[[0-9;]*m", "")
    
    return text
end

function MudClient:show_status()
    if self.connected then
        print("✅ Connected to " .. (self.host or "unknown") .. ":" .. (self.port or "unknown"))
    else
        print("❌ Not connected")
    end
end

function MudClient:run()
    self.running = true
    
    print("=== Standalone MUD Client ===")
    print("Commands:")
    print("  connect <host> <port> - Connect to MUD server")
    print("  disconnect - Disconnect from server")
    print("  status - Show connection status")
    print("  quit - Exit client")
    print("  Any other text will be sent to the MUD server")
    print()
    
    while self.running do
        -- Check for server data
        if self.connected then
            local data = self:receive_data()
            if data then
                local processed = self:process_output(data)
                print("SERVER: " .. processed)
            end
        end
        
        -- Get user input
        io.write("MUD> ")
        io.flush()
        local input = io.read()
        
        if input then
            input = input:match("^%s*(.-)%s*$")  -- trim whitespace
            
            if input == "quit" then
                self.running = false
            elseif input:match("^connect%s+(%S+)%s*(%d*)$") then
                local host, port = input:match("^connect%s+(%S+)%s*(%d*)$")
                port = port ~= "" and tonumber(port) or nil
                self:connect(host, port)
            elseif input == "disconnect" then
                self:disconnect()
            elseif input == "status" then
                self:show_status()
            elseif input == "pkuxkx" then
                self:connect("mud.pkuxkx.net", 8080)
            elseif input == "nt" then
                self:connect("nt.mud.ren", 5555)
            elseif input == "es" then
                self:connect("es2.mud.ren", 5555)
            elseif #input > 0 then
                if self.connected then
                    self:send_command(input)
                else
                    print("Not connected. Use 'connect <host> <port>' first.")
                end
            end
        end
    end
    
    -- Cleanup
    self:disconnect()
    print("MUD Client stopped.")
end

-- Create and run the client
local client = MudClient:new()
client:run()
