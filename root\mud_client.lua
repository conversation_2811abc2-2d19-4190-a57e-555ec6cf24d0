-- MUD客户端主程序
-- 使用luasocket连接MUD服务器并运行sc目录下的脚本

-- 加载配置
local config = require("config")
local socket = require("socket")
local adapter = require("sc_adapter")

-- 全局变量
local client = {}
client.connection = nil
client.connected = false
client.running = false
client.current_server = nil

-- 编码转换函数(简单实现，可能需要更复杂的编码处理)
local function convert_encoding(text, from_encoding, to_encoding)
    -- 这里需要根据实际情况实现编码转换
    -- 可以使用iconv库或其他编码转换工具
    return text
end

-- 连接到MUD服务器
function client.connect(server_name)
    server_name = server_name or "default_server"
    local server_config
    
    if server_name == "default_server" then
        server_config = config.default_server
    else
        server_config = config.servers[server_name]
    end
    
    if not server_config then
        print("错误: 未找到服务器配置 " .. server_name)
        return false
    end
    
    print("正在连接到 " .. server_config.host .. ":" .. server_config.port)
    
    -- 创建TCP连接
    client.connection = socket.tcp()
    client.connection:settimeout(config.connection.timeout)
    
    local result, err = client.connection:connect(server_config.host, server_config.port)
    if not result then
        print("连接失败: " .. (err or "未知错误"))
        return false
    end
    
    client.connected = true
    client.current_server = server_config
    print("连接成功!")
    
    return true
end

-- 断开连接
function client.disconnect()
    if client.connection then
        client.connection:close()
        client.connection = nil
    end
    client.connected = false
    print("已断开连接")
end

-- 发送数据到服务器
function client.send(data)
    if not client.connected or not client.connection then
        print("错误: 未连接到服务器")
        return false
    end
    
    -- 添加换行符
    if not string.match(data, "\n$") then
        data = data .. "\n"
    end
    
    -- 编码转换
    if client.current_server.encoding ~= "utf8" then
        data = convert_encoding(data, "utf8", client.current_server.encoding)
    end
    
    local result, err = client.connection:send(data)
    if not result then
        print("发送失败: " .. (err or "未知错误"))
        return false
    end
    
    return true
end

-- 接收数据
function client.receive()
    if not client.connected or not client.connection then
        return nil
    end
    
    client.connection:settimeout(0.1) -- 非阻塞接收
    local data, err = client.connection:receive("*l")
    
    if data then
        -- 编码转换
        if client.current_server.encoding ~= "utf8" then
            data = convert_encoding(data, client.current_server.encoding, "utf8")
        end
        return data
    elseif err == "timeout" then
        return nil -- 正常超时
    else
        print("接收数据错误: " .. (err or "未知错误"))
        return nil
    end
end

-- 主循环
function client.run()
    client.running = true

    -- 初始化适配器
    adapter.init(client)

    -- 加载脚本
    if config.script.auto_load then
        local script_path = config.script.script_dir .. "/" .. config.script.main_script
        print("加载脚本: " .. script_path)

        local success, err = pcall(dofile, script_path)
        if not success then
            print("脚本加载失败: " .. (err or "未知错误"))
        else
            print("脚本加载成功")
        end
    end
    
    print("客户端开始运行...")
    print("输入 'quit' 退出客户端")
    print("输入 'connect <server_name>' 连接到指定服务器")
    print("输入 'disconnect' 断开连接")
    
    while client.running do
        -- 处理用户输入
        io.write("> ")
        local input = io.read()
        
        if input == "quit" then
            client.running = false
        elseif input == "disconnect" then
            client.disconnect()
        elseif string.match(input, "^connect%s+(.+)") then
            local server_name = string.match(input, "^connect%s+(.+)")
            client.connect(server_name)
        elseif client.connected then
            -- 发送到MUD服务器
            client.send(input)
        else
            print("未连接到服务器")
        end
        
        -- 接收服务器数据
        if client.connected then
            local data = client.receive()
            if data then
                print(data)
                
                -- 调用脚本处理函数(如果存在)
                if OnReceive then
                    OnReceive(data, data)
                end
            end
        end
        
        -- 短暂休眠避免CPU占用过高
        socket.sleep(0.01)
    end
    
    client.disconnect()
    print("客户端已退出")
end

return client
