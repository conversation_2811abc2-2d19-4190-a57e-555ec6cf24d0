-- 简单的socket实现，用于测试连接
-- 如果luasocket不可用，我们可以使用这个基本实现

local socket = {}

-- 模拟socket.tcp()函数
function socket.tcp()
    local tcp = {}
    
    function tcp:connect(host, port)
        print("尝试连接到 " .. host .. ":" .. port)
        -- 这里我们只是模拟连接，实际上不会真正连接
        print("模拟连接成功")
        return true
    end
    
    function tcp:send(data)
        print("发送数据: " .. data)
        return #data
    end
    
    function tcp:receive(pattern)
        -- 模拟接收数据
        if pattern == "*l" then
            return "模拟服务器响应\n"
        else
            return "模拟数据"
        end
    end
    
    function tcp:close()
        print("连接已关闭")
    end
    
    function tcp:settimeout(timeout)
        print("设置超时: " .. tostring(timeout))
    end
    
    return tcp
end

return socket
