-- Simple socket implementation for testing connections
-- Use this basic implementation if luasocket is not available

local socket = {}

-- Mock socket.tcp() function
function socket.tcp()
    local tcp = {}

    function tcp:connect(host, port)
        print("Attempting to connect to " .. host .. ":" .. port)
        -- This is just a simulation, not a real connection
        print("Mock connection successful")
        return true
    end

    function tcp:send(data)
        print("Sending data: " .. data)
        return #data
    end

    function tcp:receive(pattern)
        -- Mock receiving data
        if pattern == "*l" then
            return "Mock server response\n"
        else
            return "Mock data"
        end
    end

    function tcp:close()
        print("Connection closed")
    end

    function tcp:settimeout(timeout)
        print("Setting timeout: " .. tostring(timeout))
    end

    return tcp
end

return socket
