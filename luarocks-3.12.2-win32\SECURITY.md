# Security Policy

## Supported Versions

The LuaRocks project supports the _latest version_ of the tool
for bugfixes and security updates. In other words, if an
issue is reported and we produce a fix, it will appear in a subsequent
patch version (x.y.Z) of the tool, but we do not backport fixes
to previous minor (x.Y.z) or major (X.y.z) versions.

## Reporting a Vulnerability

To report a vulnerability on the LuaRocks CLI tool, email 
<PERSON><PERSON> at <EMAIL>.

To report a vulnerability on the https://luarocks.org website,
email <PERSON> at <EMAIL>.

We will acknowledge your contact as soon as the message is
received, then assess the vulnerability and get back to you
with further feedback once analysis on our end is done.
